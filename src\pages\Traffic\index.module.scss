$header-height: 90px;
@mixin background-image($url) {
  background-image: url($url);
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}
@mixin title-style($font-size: 24px) {
  font-family: "PingFangSC", "PingFang SC";
  font-size: $font-size;
  font-style: normal;
  background: linear-gradient(180deg, #ffffff 48%, #66d9ff 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}
.traffic {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  @include background-image("@/assets/images/traffic/bgimg.png");
}

.content {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 104px);
  padding: 0 20px;
  padding-bottom: 20px;
  overflow: hidden;
}

.mainContainer {
  display: flex;
  height: 100%;
  gap: 20px;
}

.leftPanel {
  flex: 2.6;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: auto;
  @include background-image("@/assets/images/traffic/leftPanel.png");
  .leftPanelTitle {
    width: 100%;
    height: 50px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .leftPanelTitleLeft {
      @include title-style(20px);
      display: flex;
      justify-content: left;
      align-items: center;
      padding-left: 5px;
      img {
        margin-right: 10px;
      }
    }
  }
  .eventList {
    height: calc(100% - 50px);
    overflow-y: auto;
    padding: 16px;
  }
}

.rightPanel {
  flex: 1;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: auto;
  @include background-image("@/assets/images/traffic/leftPanel.png");
}
.selectIcon {
  font-size: 20px;
  color: #fff;
}
.searchWrapper {
  position: relative; // 添加相对定位
  display: flex;
  align-items: center;
  .searchInput {
    width: 300px;
    height: 35px;
    background: rgba(9, 27, 74, 0.4) !important;
    border-radius: 4px 4px 4px 4px;
    border: 1px solid rgba(92, 178, 254, 0.4);
    &:focus {
      outline: none;
    }
    padding-left: 8px;
    color: #ffffff;
  }
  .searchButtonInside {
    position: absolute; // 添加绝对定位
    right: 0; // 靠右对齐
    height: 35px;
    background-color: transparent;
    border: none;
    color: #ffffff;
    font-size: 24px;
    padding: 0 8px; // 添加内边距
    cursor: pointer; // 添加鼠标指针样式
  }
}
