$bg-image-url: "@/assets/images/traffic/leftPanel.png";
$primary-color: #0a1929;
$secondary-color: #1e3a5f;
$tertiary-color: #192a58;
$quaternary-color: #0c1c47;
$border-color: rgba(133, 174, 255, 0.25);
$text-color: #dfeaff;
$title-font: "PingFangSC", "PingFang SC";
$content-font: "Microsoft YaHei", sans-serif;
$left-width: 352px;
$right-width: 372px;

@mixin scrollbar-style {
  &::-webkit-scrollbar {
    width: 6px;
    background: #1f375b;
  }
  &::-webkit-scrollbar-thumb {
    background: #0c4d87;
    border-radius: 3px;
  }
  &::-webkit-scrollbar-track {
    background: #1f375b;
  }
}

@mixin background-image($url) {
  background-image: url($url);
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}

@mixin title-style($font-size: 24px) {
  font-family: $title-font;
  font-size: $font-size;
  font-style: normal;
  background: linear-gradient(180deg, #ffffff 48%, #66d9ff 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

@mixin border-style {
  border: 1px solid $border-color;
}

@mixin section-base-style($height: auto) {
  width: 100%;
  height: $height;
  display: flex;
}

.caseDetail {
  width: 100%;
  height: 100%;
  @include background-image($bg-image-url);

  .title {
    width: 100%;
    height: 50px;
    display: flex;
    justify-content: left;
    align-items: center;
    padding: 0 8px;
    font-family: $title-font;
    font-weight: 600;
    @include title-style(20px);

    img {
      margin-right: 10px;
    }
  }

  .content {
    width: 100%;
    height: calc(100% - 50px);
    overflow-y: auto;
    background: linear-gradient(
        180deg,
        rgba(4, 10, 30, 0.6) 0%,
        rgba(4, 10, 30, 0.8) 100%
      ),
      linear-gradient(
        180deg,
        rgba(34, 118, 252, 0) 0%,
        rgba(34, 118, 252, 0.035) 100%
      );
    box-shadow: inset 0px -1px 4px 0px rgba(41, 112, 255, 0.5);
    padding: 16px;
    color: #fff;
    font-family: $content-font;
    @include scrollbar-style;
  }
  .contrastTable {
    width: 100%;
    border-collapse: collapse;
  }

  .tableRow {
    display: flex;
    border-bottom: 1px solid #ccc;
  }

  .tableCell {
    flex: 1;
    padding: 12px;
    text-align: center;
    &.header {
      font-weight: bold;
      background-color: #f0f0f0;
    }
  }

  // 头部
  .headerSection {
    @include section-base-style(200px);

    .sectionTitle {
      width: $left-width;
      background: rgba($tertiary-color, 0.5);
      @include border-style;
      display: flex;
      justify-content: center;
      align-items: center;
      font-weight: 500;
      font-size: 18px;
      color: $text-color;
    }

    .caseHeader {
      width: $right-width;
      background: rgba($quaternary-color, 0.5);
      @include border-style;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      .caseImagePlaceholder {
        margin-bottom: 16px;
        text-align: center;

        img {
          width: 200px;
          height: 100px;
        }
      }

      .caseTitle {
        text-align: center;
        @include title-style(20px);
      }
    }
  }

  // 特征
  .featureSection {
    @include section-base-style(40px);
    border-bottom: 1px solid $secondary-color;

    .sectionTitle {
      width: $left-width;
      background: rgba($tertiary-color, 0.5);
      @include border-style;
      display: flex;
      justify-content: center;
      align-items: center;
      font-weight: 500;
      font-size: 18px;
      color: $text-color;
    }

    .featureList {
      width: $right-width;

      .featureItem {
        width: 100%;
        height: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
        background: rgba($quaternary-color, 0.5);
        border-radius: 0;
        @include border-style;
      }
    }
  }

  // 运营
  .impactSection {
    @include section-base-style(80px);
    border-bottom: 1px solid $secondary-color;

    .sectionTitle {
      width: $left-width;
      background: rgba($tertiary-color, 0.5);
      @include border-style;
      display: flex;
      justify-content: center;
      align-items: center;
      font-weight: 500;
      font-size: 18px;
      color: $text-color;
    }

    .featureList {
      width: $right-width;
      .featureItem {
        width: 100%;
        height: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
        background: rgba($quaternary-color, 0.5);
        border-radius: 0;
        @include border-style;
      }
    }
  }

  .sectionRow {
    @include section-base-style();

    .leftColumn {
      width: $left-width;
      display: flex;

      .sectionTitle {
        width: 180px;
        display: flex;
        justify-content: center;
        align-items: center;
        background: rgba($tertiary-color, 0.5);
        border-radius: 0;
        @include border-style;
        font-weight: 500;
        font-size: 18px;
        color: $text-color;
      }

      .subItemList {
        width: 180px;

        .subItem {
          width: 100%;
          height: 52px;
          background: rgba($tertiary-color, 0.5);
          border-radius: 0;
          @include border-style;
          display: flex;
          justify-content: center;
          align-items: center;
          font-weight: 500;
          font-size: 18px;
          color: $text-color;
        }
      }
    }

    .rightColumn {
      width: $right-width;

      .valueItem {
        width: 100%;
        height: 52px;
        background: rgba($quaternary-color, 0.5);
        border-radius: 0;
        @include border-style;
        display: flex;
        justify-content: center;
        align-items: center;
        font-weight: 500;
        font-size: 16px;
        color: $text-color;
      }
    }
  }

  // 评分区块
  .ratingSection {
    @include section-base-style(40px);

    .sectionTitle {
      width: $left-width;
      display: flex;
      justify-content: center;
      align-items: center;
      background: rgba($tertiary-color, 0.5);
      border-radius: 0;
      @include border-style;
      font-weight: 500;
      font-size: 18px;
      color: $text-color;
    }

    .ratingDisplay {
      width: $right-width;
      display: flex;
      justify-content: center;
      align-items: center;
      background: rgba($quaternary-color, 0.5);
      border-radius: 0;
      @include border-style;
    }
  }

  // 整体评分区块
  .overallSection {
    @include section-base-style(90px);

    .sectionTitle {
      width: $left-width;
      display: flex;
      justify-content: center;
      align-items: center;
      background: rgba($tertiary-color, 0.5);
      border-radius: 0;
      @include border-style;
      font-weight: 500;
      font-size: 18px;
      color: $text-color;
    }

    .overallContent {
      width: $right-width;
      padding: 5px;
      display: flex;
      justify-content: center;
      align-items: center;
      background: rgba($quaternary-color, 0.5);
      border-radius: 0;
      @include border-style;
    }
  }

  .starRating {
    display: flex;
    align-items: center;
    .ratingText {
      font-size: 16px;
      font-weight: 500;
      color: #ffffff;
      margin-left: 5px;
    }
  }
  .sectionTitleFen {
    width: $left-width;
    font-family: "PingFangSC";
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: 500;
    font-size: 18px;
    color: #dfeaff;
    background: rgba(43, 60, 106, 0.7);
    border-radius: 0px 0px 0px 0px;
    .ratingDisplay {
      width: $right-width;
      display: flex;
      justify-content: center;
      align-items: center;
      background: rgba($quaternary-color, 0.5);
      border-radius: 0;
    }
  }
  .icon {
    width: 92px;
    height: 28px;
    background: #081536;
    box-shadow: inset 0px 0px 12px 0px rgba(41, 202, 255, 0.5);
    border-radius: 20px 20px 20px 20px;
    border: 1px solid #29caff;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 12px;
  }
}
