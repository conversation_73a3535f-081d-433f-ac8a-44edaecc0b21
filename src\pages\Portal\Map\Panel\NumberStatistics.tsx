import clsx from "clsx";
import ReactECharts from "echarts-for-react";
import { useMemo } from "react";
import styles from "./portal.panel.module.scss";

interface DataItem {
  name: string;
  value: number;
  color: string;
}

const Legend = (d: DataItem) => {
  return (
    <div className={styles.legend}>
      <div>
        <span
          className="inline-block mr-1"
          style={{
            width: 10,
            height: 10,
            borderRadius: 2,
            backgroundColor: d.color,
          }}
        ></span>
        <span>{d.name}</span>
      </div>
      <div>
        <span
          className={clsx(styles.blueFont)}
          style={{ fontSize: 18, fontFamily: "AlimamaShuHeiTi" }}
        >
          {d.value}
        </span>
        <span className="ml-1">起</span>
      </div>
    </div>
  );
};

const colors = [
  "#0E68FF",
  "#35DDFF",
  "#3AF48E",
  "#ADDCFD",
  "#FFEB35",
  "#FF8935",
  "#FD4154",
];

const meta = [
  { name: "客运", value: 0, color: colors[0] },
  { name: "供电", value: 0, color: colors[1] },
  { name: "通号", value: 0, color: colors[2] },
  { name: "工务", value: 0, color: colors[3] },
  { name: "调度", value: 0, color: colors[4] },
  { name: "车辆", value: 0, color: colors[5] },
  { name: "客观", value: 0, color: colors[6] },
];

const getData = (evs: MockEvent[]) => {
  return meta.map((d) => {
    return {
      ...d,
      value: evs.filter((e) => e.reason === d.name).length,
    };
  });
};

const getOptions = (data: DataItem[]) => {
  return {
    tooltip: {
      trigger: "item",
    },
    legend: {
      show: false,
    },
    series: [
      {
        type: "pie",
        radius: ["67%", "86%"],
        avoidLabelOverlap: true,
        padAngle: 2,
        startAngle: 90,
        label: {
          show: false,
          position: "center",
        },
        labelLine: {
          show: false,
        },
        tooltip: {
          show: true,
          formatter: (params: {
            seriesName: string;
            dataIndex: number;
            color: any;
            value: number;
          }) => {
            const item = data[params.dataIndex];
            return `
              <span class="inline-block mr-1" style="width: 10px; height: 10px; background-color: ${params.color};"></span>
              <span class="mr-3">${item.name}</span>
              <span>${params.value}</span>
              `;
          },
        },
        data: data.map((v) => v.value),
        color: data.map((v) => v.color),
      },
      {
        type: "pie",
        radius: ["61.5%", "65%"],
        avoidLabelOverlap: true,
        padAngle: 2,
        startAngle: 90,
        label: {
          show: false,
          position: "center",
        },
        labelLine: {
          show: false,
        },
        tooltip: {
          show: false,
        },
        data: data.map((v, _i) => ({
          value: v.value,
          itemStyle: {
            color: v.color + "66",
          },
        })),
      },
    ],
  };
};

interface Props {
  events: MockEvent[];
}

function NumberStatistics(p: Props) {
  const data = useMemo(() => {
    return getData(p.events);
  }, [p.events]);
  const options = useMemo(() => {
    return getOptions(data);
  }, [data]);

  return (
    <div className="flex">
      <div style={{ padding: "4px 40px" }}>
        <div className={styles.pieBg}>
          <ReactECharts style={{ height: 205, width: 205 }} option={options} />
          <div className="absolute top-0 left-0 w-full h-full flex-center pointer-events-none">
            <div>
              <div
                className={clsx(styles.blueFont, "text-center")}
                style={{ fontSize: 36, fontFamily: "AlimamaShuHeiTi" }}
              >
                {p.events.length}
              </div>
              <div
                className="text-center"
                style={{ color: "#EAF9FF", fontSize: 16 }}
              >
                全部
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="flex-center">
        <div className="grid grid-cols-2 gap-y-3  gap-x-4">
          {data.map((d) => {
            return <Legend {...d} key={d.name} />;
          })}
        </div>
      </div>
    </div>
  );
}

export default NumberStatistics;
