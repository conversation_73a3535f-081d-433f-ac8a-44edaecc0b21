

import gulp from 'gulp';
import shell from 'gulp-shell';
import gulp_zip from 'gulp-zip';

const { series } = gulp;

const dev_server = "x:/x"; // 开发服务器地址
const stage_server = "dev:/data/projects/metro/server/frontend/web/fe"; // 预生产服务器地址
const prod_server = "x:/data/x"; // 生产服务器地址

const zipfile = 'metro-case-react.zip';
const srcfiles = ['dist/**/*'];
const basePath = './dist';

let serverValue = '';
let serverPathValue = '';

function prepare(server) {
  return cb => {
    const parts = server.split(':');
    serverValue = parts[0];
    serverPathValue = parts[1];
    cb();
  };
}

function zip() {
  return gulp.src(srcfiles, { base: basePath, encoding: false }).pipe(gulp_zip(zipfile)).pipe(gulp.dest('./dist'));
}

function compile() {
  return shell.task('npm run build')();
}

function compileStage() {
  // return shell.task('ng build --configuration=staging')();
  return shell.task('npm run build')();
}

function upload() {
  return shell.task(`scp ./dist/${zipfile} ${serverValue}:${serverPathValue}/${zipfile}`)();
}

function unzip() {
  return shell.task(`ssh ${serverValue} "cd ${serverPathValue} && unzip -o ${zipfile}"`)();
}

export const build = series(compile, zip);
export const prod = series(prepare(prod_server), compile, zip, upload, unzip);
export const dev = series(prepare(dev_server), compile, zip, upload, unzip);
export const stage = series(prepare(stage_server), compileStage, zip, upload, unzip);
