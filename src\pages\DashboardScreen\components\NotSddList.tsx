import React from "react";
import styles from "../index.module.scss";
import listIcon from "@/assets/images/home/<USER>";
import { Progress } from "antd";

const NotSddList: React.FC = () => {
  // 定义九个列表项的数据
  const listItems = [
    { title: "扣车不及时", completed: 7, total: 12 },
    { title: "未及时放行", completed: 5, total: 10 },
    { title: "未及时进行站控转换", completed: 2, total: 8 },
    { title: "错误排列进路", completed: 4, total: 9 },
    { title: "未及时关闭站台", completed: 1, total: 5 },
    { title: "列车未复位/不及时", completed: 8, total: 15 },
    { title: "未取消/及时自动功能", completed: 6, total: 13 },
    { title: "ATS操作不熟练", completed: 2, total: 7 },
    { title: "其他", completed: 7, total: 11 },
    { title: "未及时关闭站台", completed: 1, total: 5 },
    { title: "列车未复位/不及时", completed: 8, total: 15 },
    { title: "未取消/及时自动功能", completed: 6, total: 13 },
    { title: "ATS操作不熟练", completed: 2, total: 7 },
    { title: "其他", completed: 7, total: 11 },
  ];

  return (
    <div className={styles.chartContainer}>
      <div className={styles.herTitle}>
        <span></span>
        <span className={styles.title}>ATS操作不规范（45条）</span>
        <span className={styles.date}>2025/7/25</span>
      </div>
      <div className={styles.gridContainer}>
        {listItems.map((item, index) => (
          <div key={index} className={styles.list}>
            <div className={styles.left}>
              <img src={listIcon} alt="" />
            </div>
            <div className={styles.right}>
              <div className={styles.listTitle}>{item.title}</div>
              <Progress
                percent={(item.completed / item.total) * 100}
                format={() => (
                  <span className={styles.progressRedText}>
                    {item.completed} 条
                  </span>
                )}
                size="small"
                strokeColor="linear-gradient( 180deg, #18FDB0 0%, #0D89F7 100%)"
                trailColor="#1F3448"
              />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default NotSddList;
