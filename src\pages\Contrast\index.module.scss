$header-height: 90px;
$content-height: calc(100vh - #{$header-height} - 14px);
$border-color: rgba(60, 126, 255, 0.3);
$button-border-color: rgba(60, 126, 255, 0.7);
$button-hover-gradient: linear-gradient(
  180deg,
  rgba(60, 126, 255, 0.1) 0%,
  rgba(41, 196, 251, 0.7) 100%
);
$so-button-bg: rgba(180, 116, 19, 0.76);
$so-button-border: rgba(252, 167, 39, 1);
$nature-bg: rgba(24, 184, 157, 0.815);
$nature-border: rgba(47, 255, 220, 1);

@mixin background-image($url) {
  background-image: url($url);
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}

@mixin title-style($font-size: 24px) {
  font-family: "PingFangSC", "PingFang SC";
  font-size: $font-size;
  font-style: normal;
  background: linear-gradient(180deg, #ffffff 48%, #66d9ff 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

@mixin scrollbar-style {
  &::-webkit-scrollbar {
    width: 6px;
    background: #1f375b;
  }
  &::-webkit-scrollbar-thumb {
    background: #0c4d87;
    border-radius: 3px;
  }
  &::-webkit-scrollbar-track {
    background: #1f375b;
  }
}

@mixin button-style {
  width: 140px;
  height: 40px;
  color: #ffffff;
  background: rgba(60, 126, 255, 0.15);
  border-radius: 4px;
  border: 1px solid $button-border-color;
  margin-right: 10px;

  &:hover,
  &:active,
  &:focus {
    background: $button-hover-gradient;
    border: 1px solid rgba(42, 138, 255, 0.5);
  }
}

@mixin tag-button-style($bg-color, $border-color) {
  padding: 2px 8px;
  color: #fff;
  background: $bg-color;
  border-radius: 8px;
  border: 2px solid $border-color;
}

.contrast {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  @include background-image("@/assets/images/traffic/bgimg.png");
}

.content {
  width: 100%;
  height: $content-height;
  padding: 0 20px 20px;
  overflow: hidden;

  .contentText {
    width: 100%;
    height: 100%;
    @include background-image("@/assets/images/traffic/leftPanel.png");

    .title {
      width: 100%;
      height: 50px;
      display: flex;
      justify-content: left;
      align-items: center;
      padding: 0 8px;
      font-family: "PingFangSC";
      font-weight: 600;
      @include title-style(20px);

      img {
        margin-right: 10px;
      }
    }

    .contentList {
      width: 100%;
      height: calc(100% - 50px);
      padding: 10px;
      display: flex;
      flex-direction: column;

      .contentTextTop {
        height: 30%;
        width: 100%;
        border-bottom: 2px solid $border-color;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
        .contentTextTopItem {
          width: 100%;
          padding: 0 6px;
          display: flex;
          justify-content: left;
          align-items: center;
          overflow-x: auto;
          @include scrollbar-style;

          .contentAdd,
          .contentTextItem {
            width: 450px;
            height: 156px;
            display: flex;
            margin-right: 12px;
            justify-content: center;
            align-items: center;
          }

          .contentAdd {
            @include background-image(
              "@/assets/images/contrast/domesticImg.png"
            );
            font-family: "PingFangSC";
            font-weight: 500;
            font-size: 16px;
            color: #ffffff;
          }

          .contentTextItem {
            @include background-image(
              "@/assets/images/contrast/domesticBgCheck.png"
            );

            .contentTexts {
              width: 100%;
              height: 100%;
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 0 20px;

              .contentTextLeft {
                width: 30%;
                height: 80%;
                padding: 5px;
                @include background-image(
                  "@/assets/images/traffic/photobor.png"
                );

                img {
                  width: 100%;
                  height: 100%;
                }
              }

              .contentTextTitle {
                width: 100%;
                padding-left: 10px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                @include title-style(18px);
              }

              .contentTextContent {
                font-family: "PingFangSC";
                font-weight: 400;
                font-size: 14px;
                color: rgba(255, 255, 255, 0.85);
                display: flex;
                flex-wrap: wrap;
                gap: 6px;

                .contentTextSpan {
                  color: #29c4fb;
                }

                .contentTextP {
                  width: 100%;
                  height: 28px;
                  display: flex;
                  align-items: center;
                  padding-left: 10px;

                  .soButton {
                    @include tag-button-style($so-button-bg, $so-button-border);
                  }

                  .nature {
                    @include tag-button-style($nature-bg, $nature-border);
                    margin-left: 10px;
                  }
                }
              }
            }
          }
        }
        .contrastButtonGroup {
          width: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
        }
        .contrastButton {
          @include button-style;
        }
      }

      .contentTextBottom {
        height: 70%;
        width: 100%;
        .leftPanelTitle {
          width: 100%;
          height: 50px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          .leftPanelTitleLeft {
            @include title-style(20px);
            display: flex;
            justify-content: left;
            align-items: center;
            padding-left: 5px;
            font-weight: 600;
            img {
              margin-right: 10px;
              margin-top: 5px;
            }
          }
        }
        .listContainer {
          height: calc(100% - 50px);
          display: flex;
          flex-wrap: wrap;
          column-gap: 12px;
          row-gap: 20px;
          padding: 0 6px 20px 6px;
          overflow-y: auto;
          @include scrollbar-style;
        }
        .list {
          width: 450px;
          height: 252px;
          color: #fff;
          @include background-image("@/assets/images/contrast/listBg.png");
          position: relative;

          &.selected {
            @include background-image(
              "@/assets/images/contrast/listBgCheck.png"
            );
          }

          .contentTitleText {
            width: 100%;
            height: 38px;
            font-family: "PingFangSC";
            font-weight: 600;
            font-size: 18px;
            padding: 0 20px;
            display: flex;
            justify-content: left;
            align-items: center;
          }

          .contentTextSan {
            width: 100%;
            height: calc(100% - 50px);
            display: flex;
            padding: 22px 0 15px 20px;

            .contentTextLeft {
              width: 35%;
              height: 100%;
              padding: 5px;
              @include background-image("@/assets/images/traffic/photobor.png");

              img {
                width: 100%;
                height: 100%;
              }
            }

            .contentTextTitle {
              width: 100%;
              margin-bottom: 8px;
              padding-left: 10px;

              .soButton {
                @include tag-button-style($so-button-bg, $so-button-border);
              }

              .nature {
                @include tag-button-style($nature-bg, $nature-border);
                margin-left: 10px;
              }
            }

            .contentTextContent {
              font-family: "PingFangSC";
              font-weight: 400;
              font-size: 14px;
              color: rgba(255, 255, 255, 0.85);
              display: flex;
              flex-wrap: wrap;
              gap: 6px;

              .contentTextSpan {
                color: #29c4fb;
              }

              .contentTextP {
                width: 80%;
                height: 28px;
                display: flex;
                align-items: center;
                padding: 0 25px;
                @include background-image("@/assets/images/traffic/pImg.png");
              }
            }
          }

          .topRight {
            width: 116px;
            height: 28px;
            background-size: 100% 100%;
            background-position: center;
            background-repeat: no-repeat;
            position: absolute;
            top: 0;
            right: 0;
            padding: 0 15px;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1;
          }
        }
      }
    }
  }
  .searchWrapper {
    position: relative;
    display: flex;
    align-items: center;
    .searchInput {
      width: 300px;
      height: 35px;
      background: rgba(9, 27, 74, 0.4) !important;
      border-radius: 4px 4px 4px 4px;
      border: 1px solid rgba(92, 178, 254, 0.4);
      &:focus {
        outline: none;
      }
      padding-left: 8px;
      color: #ffffff;
    }
    .searchButtonInside {
      position: absolute;
      right: 0;
      height: 35px;
      background-color: transparent;
      border: none;
      color: #ffffff;
      font-size: 24px;
      padding: 0 8px;
      cursor: pointer;
    }
  }
}
