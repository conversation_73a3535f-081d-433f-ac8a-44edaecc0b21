$header-height: 90px;
$content-height: calc(100vh - #{$header-height} - 14px);
@mixin background-image($url) {
  background-image: url($url);
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}

@mixin title-style($font-size: 24px) {
  font-size: $font-size;
  font-style: normal;
  background: linear-gradient(180deg, #ffffff 48%, #66d9ff 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

@mixin scrollbar-style {
  &::-webkit-scrollbar {
    width: 6px;
    background: #1f375b;
  }
  &::-webkit-scrollbar-thumb {
    background: #0c4d87;
    border-radius: 3px;
  }
  &::-webkit-scrollbar-track {
    background: #1f375b;
  }
}
.content {
  width: 100%;
  height: $content-height;
  padding: 0 20px 20px;
  overflow: hidden;
  .contentText {
    width: 100%;
    height: 100%;
    padding: 0 16px;
    @include background-image("@/assets/images/traffic/leftPanel.png");
    .title {
      width: 100%;
      height: 50px;
      display: flex;
      justify-content: left;
      align-items: center;
      padding: 0 8px;
      font-family: "PingFangSC";
      font-weight: 600;
      @include title-style(20px);

      img {
        margin-right: 10px;
      }
    }
    .headerItem {
      width: 100%;
      height: 114px;
      padding: 17px 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 2px solid rgba(60, 126, 255, 0.3);
      .item {
        width: 484px;
        height: 80px;
        @include background-image("@/assets/images/regulations/itemBg.png");
        display: flex;
        justify-content: space-between;
        align-items: center;
        .itemRight {
          width: 280px;
          height: 72px;
          display: flex;
          justify-content: center;
          align-items: center;
          .number {
            font-family: "AlimamaShuHeiTi";
            font-weight: 700;
            font-size: 32px;
            @include title-style(32px);
          }
          .leftTitle {
            font-family: "PingFang SC";
            font-weight: 500;
            font-size: 16px;
            color: rgba(255, 255, 255, 0.85);
          }
        }
      }
      .itemTwo {
        width: 666px;
        height: 80px;
        @include background-image("@/assets/images/regulations/itemBgTwo.png");
        display: flex;
        align-items: center;
        .itemRightTwo {
          width: 464px;
          height: 72px;
          display: flex;
          justify-content: space-evenly;
          align-items: center;
          .number {
            font-family: "AlimamaShuHeiTi";
            font-weight: 700;
            font-size: 32px;
            @include title-style(32px);
          }
          .leftTitle {
            font-family: "PingFang SC";
            font-weight: 500;
            font-size: 16px;
            color: rgba(255, 255, 255, 0.85);
          }
        }
      }
      .itemLeft {
        width: 200px;
        height: 72px;
        padding: 0 8px;
        display: flex;
        justify-content: space-evenly;
        align-items: center;
        font-family: "PingFangSC";
        font-weight: 600;
        font-size: 18px;
        color: #e5f4ff;
      }
      .numbers {
        font-family: "AlimamaShuHeiTi";
        font-weight: 700;
        font-size: 32px;
        background: linear-gradient(180deg, #ffffff 48%, #ff9494 100%);
        -webkit-background-clip: text;
        background-clip: text;
        color: transparent;
      }
    }
    .contentTree {
      width: 100%;
      height: calc(100% - 165px);
      padding: 5px 0;
      display: flex;
      justify-content: space-between;
    }
  }
}
