import { diffEvents } from "@/utils/diff.util";
import {
  EVENT_MARKER_HEIGHT,
  EVENT_MARKER_WIDTH,
  getEventMarker,
  getEventPopUpDetail,
  getEventPopUpItem,
} from "@/utils/map/event.marker";
import { getLinePrimaryColor } from "@/utils/map/meta";
import { MetroLine } from "@/utils/map/metro.line.class";
import {
  divIcon,
  FeatureGroup,
  LatLngExpression,
  Map as LeafletMap,
  marker,
  Marker,
} from "leaflet";

import dispatchIcon from "@/assets/images/portal/map/reason/dispatch-icon.svg";
import electricIcon from "@/assets/images/portal/map/reason/electric-icon.svg";
import objectivesIcon from "@/assets/images/portal/map/reason/objectives-icon.svg";
import trainIcon from "@/assets/images/portal/map/reason/train-icon.svg";
import transportIcon from "@/assets/images/portal/map/reason/transport-icon.svg";
import wifiIcon from "@/assets/images/portal/map/reason/wifi-icon.svg";
import workIcon from "@/assets/images/portal/map/reason/work-icon.svg";

const reasonIconMap: Record<string, string> = {
  车辆: trainIcon,
  通号: wifiIcon,
  供电: electricIcon,
  工务: workIcon,
  调度: dispatchIcon,
  客运: transportIcon,
  客观: objectivesIcon,
};
const getReasonIcon = (reason: string) => {
  return reasonIconMap[reason] || trainIcon;
};

const markerSizeMap = [
  { scale: 0, offset: 0 }, // 09
  { scale: 0.75, offset: 0 }, // 10
  { scale: 1, offset: 0 }, // 11
  { scale: 1, offset: 20 }, // 12
  { scale: 1, offset: 35 }, // 13
  { scale: 1, offset: 40 }, // 14
  { scale: 1, offset: 45 }, // 15
  { scale: 1, offset: 45 }, // 16
];

interface LocationEventDto {
  type?: string;
  position: LatLngExpression;
  positionString: string;
  meta: MockEvent;
}

interface MarkerMapItem {
  line: string;
  marker?: Marker;
  positionString: string;
  primaryColor: string;
  data: LocationEventDto[];
  removed?: LocationEventDto[];
  added?: LocationEventDto[];
  changed?: LocationEventDto[];
  removeOrAdd?: number;
}

export function getPositionFromEvent(meta: MockEvent, lineModel: MetroLine) {
  if (meta.locationType === "自定义") {
    return meta.customPosition.split(", ").map(Number);
  }
  if (meta.locationType === "区间") {
    return meta.customPosition.split(", ").map(Number);
  }
  return lineModel.findStationByName(meta.startStation)?.meta.coord;
}

export class EventEffectService {
  private initiated = false;
  private visible = true;
  map!: LeafletMap;
  private featureGroup = new FeatureGroup([]);

  private events: MockEvent[] = [];
  private lineModels: MetroLine[] = [];
  private eventMarkers: MarkerMapItem[] = [];

  private detailListPopUpBox?: { data: LocationEventDto[]; marker: Marker };

  mount(map: LeafletMap, lineModels: MetroLine[]) {
    this.map = map;
    this.lineModels = lineModels;

    this.featureGroup.addTo(this.map);
    this.map.on("zoomend", () => {
      this.effectWithZoom();
    });
    this.map.on("click", (_ev) => {
      // this.onMapClick(ev);
    });
  }

  effectWithZoom() {
    if (!this.eventMarkers.length) return;
    // const zoom = this.map.getZoom();
    // const currentIsRemote = zoom < this.remoteDistinctZoom;
    this.rerenderAllMarkers();
  }

  rerenderAllMarkers() {
    this.eventMarkers.forEach((d) => {
      d.marker?.remove();
      d.marker = undefined;
      this.renderMarker(d);
    });
  }

  initializeLineEvents() {
    this.initiated = true;
    // this.events = evs;
    this.initializeLocationDataWithMarker(this.events);
  }

  private initializeLocationDataWithMarker(evs: MockEvent[]) {
    this.eventMarkers = [];
    const pure = evs
      .map((ev) => {
        const lineModel = this.lineModels.find((l) => l.meta.name === ev.line);
        const position = getPositionFromEvent(ev, lineModel!) || [];
        return {
          position: position as LatLngExpression,
          positionString: position.join(", "),
          meta: ev,
        };
      })
      .filter((m) => m.positionString);
    const map: Map<string, LocationEventDto[]> = new Map([]);
    pure.forEach((p) => {
      if (map.get(p.positionString!)) {
        map.get(p.positionString!)?.push(p);
      } else {
        map.set(p.positionString, [p]);
      }
    });
    Array.from(map).map(([posString, item]) => {
      const line = item[0].meta.line;
      this.eventMarkers.push({
        data: item,
        positionString: posString,
        line: line,
        primaryColor: getLinePrimaryColor(line),
      });
    });
  }

  private getLocationEventDto(ev: MockEvent, lineModel: MetroLine) {
    const position = getPositionFromEvent(ev, lineModel) || [];
    return {
      position: position as LatLngExpression,
      positionString: position.join(", "),
      meta: ev,
    };
  }
  diffEventsAndEffect(evs: MockEvent[]) {
    if (!this.initiated) {
      this.events = evs;
      return;
    }
    this.diffEventsAndEffectLocal(evs);
    this.events = evs;
  }

  private diffEventsAndEffectLocal(evs: MockEvent[]) {
    const diffResult = diffEvents(evs, this.events);
    const { added, removed, changed } = diffResult;
    this.markLocalRemoved(removed);
    this.markLocalAdded(added);
    this.markLocalChanged(changed);

    this.updateMarkerAccordingToDiff();
  }

  private markLocalAdded(evs: MockEvent[]) {
    evs.forEach((ev) => {
      const lineModel = this.lineModels.find((l) => l.meta.name === ev.line);
      const locationDto = this.getLocationEventDto(ev, lineModel!);
      const target = this.findLocationTargetDtaWithMarkerWithPosition(
        locationDto.positionString
      );
      if (target) {
        // target.data.push(locationDto);
        // target.modify = (target.modify || 0) + 1;
        // TODO mark to modify marker
        target.added = [...(target.added || []), locationDto];
        target.removeOrAdd = (target.removeOrAdd || 0) + 1;
      } else {
        const newItem: MarkerMapItem = {
          line: ev.line,
          primaryColor: getLinePrimaryColor(ev.line),
          positionString: locationDto.positionString,
          data: [], // ! Empty data is important
          added: [locationDto],
          removeOrAdd: 1,
        };
        this.eventMarkers.push(newItem);

        // TODO mark to add marker
      }
    });
  }
  private markLocalRemoved(evs: MockEvent[]) {
    evs.forEach((ev) => {
      // this.removePopupBoxIfEventRemoved(ev);
      const lineModel = this.lineModels.find((l) => l.meta.name === ev.line);
      const locationDto = this.getLocationEventDto(ev, lineModel!);
      const target = this.findLocationTargetDtaWithMarkerWithPosition(
        locationDto.positionString
      );
      if (target) {
        target.removed = [...(target.removed || []), locationDto];
        target.removeOrAdd = (target.removeOrAdd || 0) - 1;
      }
    });
  }
  private markLocalChanged(changed: EventChange[]) {
    changed.forEach((c) => {
      if (c.changes["urgentRepair"] || c.changes["severity"]) {
        const target = this.findLocationTargetDtaWithMarkerWithPosition(c.id);
        if (target) {
          const lineModel = this.lineModels.find(
            (l) => l.meta.name === c.entity.line
          );
          const locationDto = this.getLocationEventDto(c.entity, lineModel!);
          target.changed = [locationDto];
        }
      }
    });
  }

  private updateMarkerAccordingToDiff() {
    this.eventMarkers.forEach((d) => {
      if (!d.removed && !d.added && !d.changed) return;

      this.renderOrUpdateDataWithLocalMarker(d);

      d.added = undefined;
      d.removed = undefined;
      d.changed = undefined;
      d.removeOrAdd = undefined;
    });
    this.eventMarkers = this.eventMarkers.filter((d) => d.data.length);
  }

  findLocationTargetDtaWithMarkerWithPosition(s: string) {
    return this.eventMarkers.find((d) => d.positionString === s);
  }

  private renderOrUpdateDataWithLocalMarker(d: MarkerMapItem) {
    const previousCount = d.data.length;
    const currentCount = previousCount + (d.removeOrAdd || 0);
    if (currentCount === 0) {
      // 这个位置之前有数据，现在没有数据
      d.marker?.remove();
      d.marker = undefined;
      if (d.data.length === 1) {
        // 之前只有一个, 现在没了
        this.removePopupBoxIfEventRemoved(d.data[0].meta);
        // this.clearEffectWithLocalItem(d);
        this.attachLocalModify(d);
      } else {
        // 之前有多个，现在没了
        this.attachLocalModify(d);
      }
      return;
    }

    if (currentCount === 1) {
      // 之前有多个，现在只有一个
      this.attachLocalModify(d);
      this.renderLocalDegrade(d);
      return;
    }

    if (currentCount > 1) {
      // 之前有多个，现在有多个
      this.attachLocalModify(d);
      this.renderLocalUpgrade(d);
      return;
    }

    if (previousCount === 0) {
      // 这个位置之前没有数据，现在有数据
      this.rerenderLocal(d);
      return;
    }
  }

  private renderLocalUpgrade(d: MarkerMapItem) {
    d.marker?.remove();
    d.marker = undefined;
    this.rerenderLocal(d);
    if (
      this.detailListPopUpBox?.data[0].positionString ===
      d.data[0].positionString
    ) {
      this.rerenderPopUpBox(d.data);
    }
  }

  private attachLocalModify(d: MarkerMapItem) {
    d.data = d.data.filter(
      (ev) => !d.removed?.find((r) => r.meta.id === ev.meta.id)
    );
    d.data.push(...(d.added || []));

    d.changed?.forEach((c) => {
      const alter = d.data.find((d) => d.meta.id === c.meta.id);
      if (alter) {
        Object.assign(alter.meta, c.meta);
      }
    });
  }

  private renderLocalDegrade(d: MarkerMapItem) {
    d.marker?.remove();
    d.marker = undefined;
    if (this.detailListPopUpBox?.data[0].positionString === d.positionString) {
      this.rerenderPopUpBox(d.data);
    }
    // this.removePopupBoxIfEventRemoved(d.data[0].meta);
    // this.clearEffectWithLocalItem(d);
    this.renderMarker(d);
  }
  private renderMarker(item: MarkerMapItem) {
    const marker = this.getMarker(item);
    if (this.visible) {
      this.featureGroup.addLayer(marker);
    }
    item.marker = marker;
  }
  private rerenderLocal(d: MarkerMapItem) {
    d.marker?.remove();
    d.marker = undefined;
    if (d.data.length === 1) {
      this.renderMarker(d);
      // this.renderSingleItem(d);
    } else if (d.data.length > 1) {
      this.renderMarker(d);
      // this.renderComposeItem(d);
    }
  }
  private removePopupBoxIfEventRemoved(ev: MockEvent) {
    const id = ev.id;
    if (this.detailListPopUpBox?.data.map((d) => d.meta.id).includes(id)) {
      this.removeDetailListPopUpBox();
    }
  }

  private getMarkerSize(zoom: number) {
    const minZoom = 9;
    return markerSizeMap[zoom - minZoom] || { scale: 0, offset: 0 };
  }

  private getMarker(item: MarkerMapItem) {
    const dto = item.data[0];
    const zoom = this.map.getZoom();
    const { scale } = this.getMarkerSize(zoom);
    const reason = dto.meta.reason;

    const m = marker(dto.position as LatLngExpression, {
      icon: getEventMarker(item.primaryColor, getReasonIcon(reason), scale),
    }) as Marker;

    m.on("click", (_p) => {
      this.toggleListPopUpBox(item.data);
    });

    return m;
  }

  private toggleListPopUpBox(data: LocationEventDto[]) {
    if (this.detailListPopUpBox?.data) {
      if (
        this.detailListPopUpBox?.data[0].positionString ===
        data[0].positionString
      ) {
        this.removeDetailListPopUpBox();
        return;
      }
      this.removeDetailListPopUpBox();
    }
    this.renderDetailListPopUpBox(data);
  }
  private renderDetailListPopUpBox(data: LocationEventDto[]) {
    const m = this.getDetailListPopUpBox(data);
    this.detailListPopUpBox = {
      data,
      marker: m,
    };
    m.addTo(this.map);
    m.setZIndexOffset(1000);
  }

  private removeDetailListPopUpBox() {
    this.detailListPopUpBox?.marker.remove();
    this.detailListPopUpBox = undefined;
  }

  private rerenderPopUpBox(data: LocationEventDto[]) {
    this.removeDetailListPopUpBox();
    this.renderDetailListPopUpBox(data);
  }

  private getDetailListPopUpBox(data: LocationEventDto[]) {
    const scale = this.getMarkerSize(this.map.getZoom()).scale;
    const position = data[0].position;
    const color = getLinePrimaryColor(data[0].meta.line);
    const targetHeight = EVENT_MARKER_HEIGHT;
    const targetWidth = EVENT_MARKER_WIDTH;
    const itemHeight = 44;
    const boxHeight = data.length * (itemHeight + 2 + 1 + 60);
    const boxWidth = 300;

    const m = marker(position as LatLngExpression, {
      icon: divIcon({
        html: `<div  class="w-full h-full relative animate__animated animate__zoomInLeft">
          ${data
            .map((d) => {
              return (
                getEventPopUpItem(
                  color,
                  getReasonIcon(d.meta.reason),
                  d.meta.startStation,
                  scale
                ) + getEventPopUpDetail(color, d.meta)
              );
            })
            .join("")}
        </div>
          
        `,
        className: "hover-border",
        iconSize: [boxWidth, boxHeight],
        iconAnchor: [-targetWidth / 2, targetHeight * 0.66 + boxHeight / 2],
      }),
    }) as Marker;
    // m.on('click', (p) => {
    // });
    return m;
  }

  setVisibility(visible: boolean) {
    this.visible = visible;
    if (!this.visible) {
      this.hideEffect();
    } else {
      this.revertEffect();
    }
  }

  private hide() {
    this.featureGroup.clearLayers();
    this.removeDetailListPopUpBox();
  }
  private hideEffect() {
    this.hide();
  }

  private revert() {
    this.eventMarkers.forEach((e) => {
      e.marker && this.featureGroup.addLayer(e.marker);
    });
    this.eventMarkers.forEach((e) => {
      e.marker && this.featureGroup.addLayer(e.marker);
    });
  }

  private revertEffect() {
    if (!this.visible) return;
    this.revert();
  }
}

export const eventEffectService = new EventEffectService();
