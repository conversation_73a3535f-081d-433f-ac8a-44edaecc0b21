import { createSlice, PayloadAction } from "@reduxjs/toolkit";

interface NavigationState {
  activeButton: string | null;
  activeTab: string | null;
}

const initialState: NavigationState = {
  activeButton: null,
  activeTab: null,
};

const navigationSlice = createSlice({
  name: "navigation",
  initialState,
  reducers: {
    setActiveButton: (state, action: PayloadAction<string>) => {
      state.activeButton = action.payload;
    },
    setActiveTab: (state, action: PayloadAction<string>) => {
      state.activeTab = action.payload;
    },
  },
});

export const { setActiveButton, setActiveTab } = navigationSlice.actions;
export default navigationSlice.reducer;
