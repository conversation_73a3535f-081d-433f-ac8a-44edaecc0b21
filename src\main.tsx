import zhC<PERSON> from "antd/locale/zh_CN";
import ReactD<PERSON> from "react-dom/client";
import { Provider } from "react-redux";
import { BrowserRouter } from "react-router-dom";
import "virtual:uno.css";
import RouterComponent from "./router/index";
import { store } from "./store/index";
// 导入全局样式
import { ConfigProvider } from "antd";
import dayjs from "dayjs";
import "dayjs/locale/zh-cn";
import "./index.scss";
dayjs.locale("zh-cn");
const root = ReactDOM.createRoot(
  document.getElementById("root") as HTMLElement
);
root.render(
  <ConfigProvider locale={zhCN}>
    <BrowserRouter basename="/fe">
      <Provider store={store}>
        <RouterComponent />
      </Provider>
    </BrowserRouter>
  </ConfigProvider>
);
