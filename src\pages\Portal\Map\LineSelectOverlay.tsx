import ArrowSvg from "@/assets/images/portal/map/arrow.svg?react";
import { getLineNameAlias, lineColorArray2025 } from "@/utils/map/meta";
import { useBoolean, useUpdateEffect } from "ahooks";
import clsx from "clsx";
import { useMemo, useRef, useState } from "react";
import styles from "./select.overlay.module.scss";

const BUTTON_WIDTH = 88 + 6;

const ButtonSvgWrapper = (props: { selected: boolean }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      fill="none"
      version="1.1"
      width="88"
      height="36"
      viewBox="0 0 88 36"
    >
      <defs>
        <filter
          id="master_svg0_204_05660"
          filterUnits="objectBoundingBox"
          colorInterpolationFilters="sRGB"
          x="0"
          y="0"
          width="1"
          height="1"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="BackgroundImageFix"
            result="shape"
          />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            result="hardAlpha"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          />
          <feOffset dy="0" dx="0" />
          <feGaussianBlur stdDeviation="8" />
          <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0.16078431904315948 0 0 0 0 0.7921568751335144 0 0 0 0 1 0 0 0 0.5 0"
          />
          <feBlend mode="normal" in2="shape" result="effect1_innerShadow" />
        </filter>
        <radialGradient
          cx="0"
          cy="0"
          r="1"
          gradientUnits="userSpaceOnUse"
          id="master_svg1_115_56954"
          gradientTransform="translate(44 31.821532487869263) rotate(90) scale(31.22803044319153 76.33518552780151)"
        >
          <stop
            offset="0%"
            stopColor="#29C4FB"
            stopOpacity="0.699999988079071"
          />
          <stop offset="100%" stopColor="#29C4FB" stopOpacity="0" />
        </radialGradient>
        <linearGradient
          x1="0.5"
          y1="0"
          x2="0.5"
          y2="1"
          id="master_svg2_106_10376"
        >
          <stop offset="0%" stopColor="#29CAFF" stopOpacity="1" />
          <stop
            offset="100%"
            stopColor="#29CAFF"
            stopOpacity="0.4000000059604645"
          />
        </linearGradient>
      </defs>
      <g></g>
      {props.selected ? selectedSvg : normalSvg}
    </svg>
  );
};

const normalSvg = (
  <g>
    <g>
      <path
        d="M0,30L5.41421,35.4142C5.78929,35.7893,6.29799,36,6.82843,36L81.1716,36C81.702,36,82.2107,35.7893,82.5858,35.4142L88,30L88,6L82.5858,0.585786C82.2107,0.210714,81.702,0,81.1716,0L6.82843,0C6.29799,0,5.78929,0.210714,5.41421,0.585786L0,6L0,30Z"
        fill="#000000"
        fillOpacity="0.30000001192092896"
      />
      <path
        d="M0,30L5.41421,35.4142C5.78929,35.7893,6.29799,36,6.82843,36L81.1716,36C81.702,36,82.2107,35.7893,82.5858,35.4142L88,30L88,6L82.5858,0.585786C82.2107,0.210714,81.702,0,81.1716,0L6.82843,0C6.29799,0,5.78929,0.210714,5.41421,0.585786L0,6L0,30Z"
        fill="#025DF4"
        fillOpacity="0.20000000298023224"
      />
      <path
        d="M0,30L5.41421,35.4142C5.78929,35.7893,6.29799,36,6.82843,36L81.1716,36C81.702,36,82.2107,35.7893,82.5858,35.4142L88,30L88,6L82.5858,0.585786C82.2107,0.210714,81.702,0,81.1716,0L6.82843,0C6.29799,0,5.78929,0.210714,5.41421,0.585786L0,6L0,30ZM87,6.41421L87,29.5858L81.8787,34.7071Q81.5858,35,81.1716,35L6.82843,35Q6.41421,35,6.12132,34.7071L1,29.5858L1,6.41421L6.12132,1.29289Q6.41421,1,6.82843,1L81.1716,1Q81.5858,1,81.8787,1.29289L87,6.41421Z"
        fillRule="evenodd"
        fill="#025DF4"
        fillOpacity="0.4000000059604645"
      />
    </g>
  </g>
);
const selectedSvg = (
  <g>
    <g filter="url(#master_svg0_204_05660)">
      <path
        d="M0,30L5.41421,35.4142C5.78929,35.7893,6.29799,36,6.82843,36L81.1716,36C81.702,36,82.2107,35.7893,82.5858,35.4142L88,30L88,6L82.5858,0.585786C82.2107,0.210714,81.702,0,81.1716,0L6.82843,0C6.29799,0,5.78929,0.210714,5.41421,0.585786L0,6L0,30Z"
        fill="#000000"
        fillOpacity="0.30000001192092896"
      />
      <path
        d="M0,30L5.41421,35.4142C5.78929,35.7893,6.29799,36,6.82843,36L81.1716,36C81.702,36,82.2107,35.7893,82.5858,35.4142L88,30L88,6L82.5858,0.585786C82.2107,0.210714,81.702,0,81.1716,0L6.82843,0C6.29799,0,5.78929,0.210714,5.41421,0.585786L0,6L0,30Z"
        fill="#025DF4"
        fillOpacity="0.10000000149011612"
      />
      <path
        d="M0,30L5.41421,35.4142C5.78929,35.7893,6.29799,36,6.82843,36L81.1716,36C81.702,36,82.2107,35.7893,82.5858,35.4142L88,30L88,6L82.5858,0.585786C82.2107,0.210714,81.702,0,81.1716,0L6.82843,0C6.29799,0,5.78929,0.210714,5.41421,0.585786L0,6L0,30Z"
        fill="url(#master_svg1_115_56954)"
        fillOpacity="0.4000000059604645"
      />
      <path
        d="M0,30L5.41421,35.4142C5.78929,35.7893,6.29799,36,6.82843,36L81.1716,36C81.702,36,82.2107,35.7893,82.5858,35.4142L88,30L88,6L82.5858,0.585786C82.2107,0.210714,81.702,0,81.1716,0L6.82843,0C6.29799,0,5.78929,0.210714,5.41421,0.585786L0,6L0,30ZM87,6.41421L87,29.5858L81.8787,34.7071Q81.5858,35,81.1716,35L6.82843,35Q6.41421,35,6.12132,34.7071L1,29.5858L1,6.41421L6.12132,1.29289Q6.41421,1,6.82843,1L81.1716,1Q81.5858,1,81.8787,1.29289L87,6.41421Z"
        fillRule="evenodd"
        fill="#025DF4"
        fillOpacity="0.4000000059604645"
      />
      <path
        d="M0,30L5.41421,35.4142C5.78929,35.7893,6.29799,36,6.82843,36L81.1716,36C81.702,36,82.2107,35.7893,82.5858,35.4142L88,30L88,6L82.5858,0.585786C82.2107,0.210714,81.702,0,81.1716,0L6.82843,0C6.29799,0,5.78929,0.210714,5.41421,0.585786L0,6L0,30ZM87,6.41421L87,29.5858L81.8787,34.7071Q81.5858,35,81.1716,35L6.82843,35Q6.41421,35,6.12132,34.7071L1,29.5858L1,6.41421L6.12132,1.29289Q6.41421,1,6.82843,1L81.1716,1Q81.5858,1,81.8787,1.29289L87,6.41421Z"
        fillRule="evenodd"
        fill="url(#master_svg2_106_10376)"
        fillOpacity="1"
      />
    </g>
  </g>
);

interface LineButtonProps {
  name: string;
  color?: string;
  selected: boolean;
  onClick?: () => void;
}
const LineButton = (p: LineButtonProps) => {
  return (
    <div className={styles.lineButton} onClick={p.onClick}>
      <ButtonSvgWrapper selected={p.selected} />
      <div className={styles.lineContent}>
        {p.color && (
          <div className={styles.lineColor}>
            <div
              className={styles.colorBox}
              style={{ backgroundColor: p.color }}
            />
          </div>
        )}
        <div className={styles.lineName}>{p.name}</div>
      </div>
    </div>
  );
};

interface Props {
  onChange: (lines: string[]) => void;
}

export const LineSelectOverlay = (p: Props) => {
  const [lines, setLines] = useState(
    lineColorArray2025.map((l) => ({
      name: l[0],
      color: l[1],
      alias: getLineNameAlias(l[0]),
      checked: true,
    }))
  );
  const allChecked = useMemo(() => lines.every((l) => l.checked), [lines]);
  useUpdateEffect(() => {
    p.onChange(lines.filter((l) => l.checked).map((l) => l.name));
  }, [lines]);

  const [scrollLock, { set: setScrollLock }] = useBoolean(false);
  const [arrowVisible, setArrowVisible] = useState({
    left: false,
    right: true,
  });

  const outerRef = useRef<HTMLDivElement>(null);
  const innerRef = useRef<HTMLDivElement>(null);

  const handleLineClick = (name: string) => {
    setLines((lines) => {
      return lines.map((l) => {
        if (l.name === name) {
          return {
            ...l,
            checked: !l.checked,
          };
        }
        return l;
      });
    });
  };
  const handleAllClick = () => {
    setLines((lines) => {
      return lines.map((l) => {
        return {
          ...l,
          checked: !allChecked,
        };
      });
    });
  };

  const setDirectorVisible = () => {
    const outer = outerRef.current!;
    const { scrollWidth, scrollLeft, clientWidth } = outer;
    setArrowVisible({
      left: scrollLeft > 0,
      right: clientWidth + scrollLeft < scrollWidth,
    });
  };

  const handleScroll = (direction: "left" | "right") => {
    const outer = outerRef.current!;
    if (scrollLock) return;
    setScrollLock(true);
    const scrollLeft =
      direction === "left"
        ? outer.scrollLeft - BUTTON_WIDTH * 5.01
        : outer.scrollLeft + BUTTON_WIDTH * 5.01;
    outer.scrollTo({
      left: scrollLeft,
      behavior: "smooth",
    });
    setTimeout(() => {
      setDirectorVisible();
      setScrollLock(false);
    }, 500);
  };

  return (
    <div className={styles.overlay}>
      <div className={styles.contentBox}>
        <div ref={outerRef} className={styles.scrollBox}>
          <div ref={innerRef} className={styles.scrollContent}>
            <LineButton
              name="全部线路"
              selected={allChecked}
              onClick={handleAllClick}
            />
            {lines.map((l) => (
              <LineButton
                key={l.name}
                name={l.alias}
                color={l.color}
                selected={l.checked}
                onClick={() => handleLineClick(l.name)}
              />
            ))}
          </div>
        </div>

        <ArrowSvg
          className={clsx(styles.leftArrow, {
            hidden: !arrowVisible.left,
          })}
          onClick={() => handleScroll("left")}
        />
        <ArrowSvg
          className={clsx(styles.rightArrow, {
            hidden: !arrowVisible.right,
          })}
          onClick={() => handleScroll("right")}
        />
      </div>
    </div>
  );
};
