.portalFilter {
  position: absolute;
  top: calc(var(--header-height) - var(--reserve-top-height) + 10px);
  left: 20px;
  width: 172px;
  height: auto;
  z-index: 800;
  // background-color: rgba(2, 93, 244, 0.6);
}

@mixin btnCommon {
  width: 172px;
  height: 44px;
  border: 1px solid;
  border-image: linear-gradient(
      270deg,
      rgba(255, 255, 255, 0),
      rgba(255, 255, 255, 0.1),
      rgba(167.4, 167.4, 167.4, 0.7),
      rgba(0, 0, 0, 0)
    )
    1 1;

  --at-apply: flex items-center cursor-pointer pointer-events-auto select-none;
}

.categoryItem {
  font-weight: 500;
  font-size: 16px;
  color: #eaf9ff;
  .categoryButton {
    background: linear-gradient(
        270deg,
        rgba(24, 45, 99, 0) 0%,
        rgba(24, 45, 99, 0.4) 26%,
        rgba(22, 42, 96, 0.4) 68%,
        rgba(19, 39, 94, 0) 100%
      ),
      linear-gradient(
        270deg,
        rgba(63, 142, 240, 0) 0%,
        rgba(63, 142, 240, 0.12) 41%,
        rgba(63, 142, 240, 0) 78%
      );
    @include btnCommon;
    padding-left: 12px;
    .iconContainer {
      width: 1.5rem;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      img {
        width: 16px;
        height: auto;
      }
    }
    .label {
      min-width: 78px;
      text-align: center;
    }

    &.active {
      background: linear-gradient(
          270deg,
          rgba(24, 45, 99, 0) 0%,
          rgba(24, 45, 99, 0.8) 26%,
          rgba(22, 42, 96, 0.8) 68%,
          rgba(19, 39, 94, 0) 100%
        ),
        linear-gradient(
          270deg,
          rgba(24, 45, 99, 0) 0%,
          rgba(24, 45, 99, 0.4) 26%,
          rgba(22, 42, 96, 0.4) 68%,
          rgba(19, 39, 94, 0) 100%
        ),
        linear-gradient(
          270deg,
          rgba(41, 196, 251, 0) 0%,
          rgba(41, 196, 251, 0.15) 41%,
          rgba(41, 196, 251, 0) 78%
        );
    }
  }
}

.subOptions {
  padding-top: 2px;
  overflow: hidden;
  transition: height 0.3s ease-in-out;
}

.subOption {
  background: linear-gradient(
    270deg,
    rgba(24, 45, 99, 0) 0%,
    rgba(24, 45, 99, 0.4) 26%,
    rgba(22, 42, 96, 0.4) 68%,
    rgba(19, 39, 94, 0) 100%
  );
  @include btnCommon;
  margin-bottom: 2px;
  padding-left: 28px;

  .label {
    --at-apply: flex-center;
  }

  .dot {
    width: 6px;
    height: 6px;
    background: #eaf9ff;
    --at-apply: rounded inline-block mr-2;
  }
  &.active {
    background-image: url("@/assets/images/portal/map/filter/filter-option-bg-active.svg");
    .label {
      color: #29caff;
    }
    .dot {
      background: #29caff;
    }
  }
}
