import { isEqual } from "./assistant";

export function diffEvents(
  current: MockEvent[],
  previous: MockEvent[]
): EventDiffResult {
  // 将数组转为 Map 以便快速查找（使用 id 作为键）
  const prevMap = new Map<string, MockEvent>(
    previous.map((event) => [event.id, event])
  );
  const currentMap = new Map<string, MockEvent>(
    current.map((event) => [event.id, event])
  );

  // 1. 找出新增事件（存在于 current 但不存在于 previous）
  const added = current.filter((event) => !prevMap.has(event.id));

  // 2. 找出删除事件（存在于 previous 但不存在于 current）
  const removed = previous.filter((event) => !currentMap.has(event.id));

  // 3. 找出属性变化的事件
  const changed: EventChange[] = [];
  current.forEach((currentEvent) => {
    const prevEvent = prevMap.get(currentEvent.id);
    if (prevEvent) {
      // 只处理两个列表中都存在的事件
      const changes: Record<string, { old: any; new: any; entity: MockEvent }> =
        {};

      // 遍历所有属性进行对比（排除 id 和 createTime）
      (Object.keys(currentEvent) as (keyof MockEvent)[]).forEach((key) => {
        if (key === "id") return; // 这些字段不需要比较

        const currentValue = currentEvent[key];
        const previousValue = prevEvent[key];

        // 深度比较（处理数组/对象的情况）
        if (!isEqual(currentValue, previousValue)) {
          changes[key] = {
            old: previousValue,
            new: currentValue,
            entity: currentEvent,
          };
        }
      });

      if (Object.keys(changes).length > 0) {
        changed.push({
          id: currentEvent.id,
          changes,
          entity: currentEvent,
        });
      }
    }
  });

  return { added, removed, changed };
}
