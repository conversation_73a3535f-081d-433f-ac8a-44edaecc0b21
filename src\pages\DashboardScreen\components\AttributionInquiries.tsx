import React from "react";
import { But<PERSON>, Result } from "antd";
import { Link } from "react-router-dom";

const AttributionInquiries: React.FC = () => {
  return (
    <div>
      <Result
        status="404"
        title={<span style={{ color: "white" }}>页面未开发</span>}
        subTitle={
          <span style={{ color: "white" }}>该功能正在开发中，敬请期待</span>
        }
        extra={
          <Link to="/">
            <Button type="primary">返回首页</Button>
          </Link>
        }
      />
    </div>
  );
};

export default AttributionInquiries;
