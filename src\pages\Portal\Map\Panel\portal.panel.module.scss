.portalPanel {
  position: absolute;
  top: calc(var(--header-height) - var(--reserve-top-height));
  right: 20px;
  width: var(--panel-width);
  height: calc(100vh - var(--header-height) - 20px);
  z-index: 800;

  background: linear-gradient(
    270deg,
    rgba(24, 45, 99, 0.7) 0%,
    rgba(19, 39, 94, 0.8) 98%
  );
  border: 1px solid rgba(2, 93, 244, 0.2);
  border-radius: 12px;

  .header {
    height: 50px;
    line-height: 50px;
    background-image: url("@/assets/images/portal/map/panel/header-bg.png");
    background-size: 100% 100%;
    background-position: left;
    background-repeat: no-repeat;
    padding: 0 32px 2px 36px;
    color: #eaf9ff;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .headerFont {
      font-family: "AlimamaShuHeiTi";
      font-weight: 700;
      font-size: 20px;
    }
  }
}

.blueFont {
  // text-shadow: 0px 4px 8px rgba(8, 22, 43, 0.6);
  font-style: normal;
  background-image: linear-gradient(180deg, #ffffff 46%, #66d9ff 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}
.titleFont {
  font-weight: 600;
  font-size: 18px;
  text-shadow: 0px 4px 12px rgba(8, 22, 43, 0.1),
    0px 4px 12px rgba(8, 22, 43, 0.1);
}

.annualSelect {
  width: 102px;
  :global(.ant-select-selector) {
    border: 1px solid rgba(2, 93, 244, 0.4) !important;
  }
}

.title {
  padding: 12px 0 12px 20px;
}

.triangle {
  display: inline-block;
  border-style: solid;
  border-color: transparent;
  border-width: 5px 0 5px 5px;
  border-left-color: #29caff;
}

.pieBg {
  position: relative;
  width: 205px;
  height: 205px;
  background: url("@/assets/images/portal/map/panel/pie-bg.png");
  background-size: 98% 98%;
  background-position: center;
  background-repeat: no-repeat;
}

.legend {
  width: 130px;
  height: 36px;
  line-height: 36px;
  padding: 0 10px;
  color: #eaf9ff;
  background: linear-gradient(
    90deg,
    rgba(157, 193, 255, 0.12) 0%,
    rgba(177, 205, 255, 0) 100%
  );
  border-radius: 4px 0px 0px 4px;
  display: flex;
  justify-content: space-between;
}
