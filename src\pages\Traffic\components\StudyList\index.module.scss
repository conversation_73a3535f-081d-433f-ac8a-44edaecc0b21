$bg-color-panel: rgba(11, 30, 77, 0.2);
$border-color: rgba(2, 93, 244, 0.1);
$shadow-inner: inset 0px 0px 20px 0px rgba(41, 112, 255, 0.3);

@mixin background-image($url) {
  background-image: url($url);
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}

@mixin title-style($font-size: 24px) {
  font-family: "PingFangSC", "PingFang SC";
  font-size: $font-size;
  font-style: normal;
  background: linear-gradient(180deg, #ffffff 48%, #66d9ff 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

@mixin scrollbar-style {
  &::-webkit-scrollbar {
    width: 6px;
    background: #1f375b;
  }
  &::-webkit-scrollbar-thumb {
    background: #0c4d87;
    border-radius: 3px;
  }
  &::-webkit-scrollbar-track {
    background: #1f375b;
  }
}

// 新增：公共面板样式mixin
@mixin panel-style($gradient-color) {
  width: 246px;
  height: 96px;
  background: $bg-color-panel;
  box-shadow: $shadow-inner;
  border-radius: 0;
  border: 1px solid $border-color;
  display: flex;
  justify-content: space-between;
  padding: 5px;
  align-items: center;
  .learnedNum {
    font-family: "PingFangSC";
    font-weight: 600;
    font-size: 32px;
    text-shadow: 0px 4px 12px rgba(8, 22, 43, 0.5);
    font-style: normal;
    background: linear-gradient(180deg, #ffffff 48%, $gradient-color 100%);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
  }
  .learned {
    font-family: "PingFangSC";
    font-weight: 600;
    font-size: 16px;
    color: rgba(153, 196, 211, 0.8);
    line-height: 22px;
    text-shadow: 0px 4px 12px rgba(8, 22, 43, 0.5);
    font-style: normal;
    text-transform: none;
    padding-left: 9px;
  }
}

.rightPanel {
  width: 100%;
  height: 100%;

  .rightPanelTitle {
    width: 100%;
    height: 50px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .rightPanelTitleLeft {
      @include title-style(20px);
      display: flex;
      justify-content: left;
      align-items: center;
      padding-left: 5px;

      img {
        margin-right: 10px;
      }
    }

    .selectIcon {
      font-size: 20px;
      color: #fff;
    }
  }

  .rightPanelTop {
    width: 100%;
    height: 130px;
    display: flex;
    justify-content: space-around;
    align-items: center;

    .rightPanelTopLeft {
      @include panel-style(#66d9ff);
    }

    .rightPanelTopRight {
      @include panel-style(#ff9494);
    }
  }

  .learnedRecord {
    font-family: "PingFangSC";
    font-weight: 600;
    font-size: 16px;
    color: #bcddff;
    line-height: 22px;
    text-shadow: 0px 4px 12px rgba(8, 22, 43, 0.5),
      0px 4px 12px rgba(8, 22, 43, 0.5);
    font-style: normal;
    text-transform: none;
  }

  .rightPanelContent {
    width: 100%;
    height: calc(100% - 130px - 50px);
    overflow-x: auto;
    padding: 0 16px;
    @include scrollbar-style;

    .rightPanelContentList {
      width: 100%;
      height: 176px;
      margin-bottom: 17px;
      @include background-image("@/assets/images/traffic/studyTasksBg.png");
      &.selected {
        @include background-image("@/assets/images/traffic/studyTasks.png");
      }
      .ListTitle {
        width: 100%;
        height: 38px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 20px;

        .ListTitleText {
          width: 80px;
          height: 26px;
          font-family: "PingFangSC";
          font-weight: 600;
          font-size: 14px;
          color: #3af48e;
          display: flex;
          justify-content: center;
          align-items: center;
          background: linear-gradient(
              180deg,
              rgba(58, 244, 142, 0.1) 15%,
              rgba(58, 244, 142, 0.3) 100%
            ),
            rgba(12, 36, 89, 0.5);
          border-radius: 4px;
          border: 1px solid rgba(58, 244, 142, 0.4);
        }
      }

      .ListContent {
        width: 100%;
        height: calc(100% - 38px - 50px);
        display: flex;
        flex-wrap: wrap;
        padding-left: 4%;
        padding-right: 4%;
        padding-top: 4%;

        img {
          padding: 0 5px;
        }

        .content-item {
          width: 50%;
        }
      }

      .ListContentButton {
        width: 92%;
        height: 40px;
        background: rgba(2, 93, 244, 0.16);
        border-radius: 8px;
        border: 1px solid rgba(2, 93, 244, 0.16);
        margin-left: 4%;
        display: flex;
        justify-content: space-around;
        align-items: center;

        div {
          width: 70%;
          height: 100%;
          font-family: "PingFangSC";
          font-weight: 600;
          font-size: 16px;
          color: rgba(234, 249, 255, 0.8);
          line-height: 38px;
          text-shadow: 0px 4px 12px rgba(8, 22, 43, 0.5);
        }

        button {
          width: 90px;
          height: 26px;
          background: radial-gradient(
              at 0% 88%,
              rgba(41, 196, 251, 0.7) 0%,
              rgba(41, 196, 251, 0) 100%
            ),
            rgba(60, 126, 255, 0.3);
          border-radius: 4px;
          border: 1px solid rgba(42, 138, 255, 0.5);
          font-weight: 500;
          font-size: 14px;
          color: #ffffff;
        }
      }
    }
  }
}
