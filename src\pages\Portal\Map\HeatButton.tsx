import HeatIcon from "@/assets/images/portal/map/heat-icon.png";
import { useBoolean, useUpdateEffect } from "ahooks";
import { Checkbox } from "antd";
import clsx from "clsx";
import styles from "./portal.map.module.scss";

const bgSvg = (
  <svg
    className="align-bottom"
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
    fill="none"
    version="1.1"
    width="132"
    height="40"
    viewBox="0 0 132 40"
  >
    <defs>
      <filter
        id="master_svg0_204_06335"
        filterUnits="objectBoundingBox"
        colorInterpolationFilters="sRGB"
        x="0"
        y="0"
        width="1"
        height="1"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dy="0" dx="0" />
        <feGaussianBlur stdDeviation="4" />
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0.16470588743686676 0 0 0 0 0.7921568751335144 0 0 0 0 1 0 0 0 0.5 0"
        />
        <feBlend mode="normal" in2="shape" result="effect1_innerShadow" />
      </filter>
      <radialGradient
        cx="0"
        cy="0"
        r="1"
        gradientUnits="userSpaceOnUse"
        id="master_svg1_200_18384"
        gradientTransform="translate(66 35.357258319854736) rotate(90) scale(34.69781160354614 114.50277829170227)"
      >
        <stop offset="0%" stopColor="#29C4FB" stopOpacity="0.699999988079071" />
        <stop offset="100%" stopColor="#29C4FB" stopOpacity="0" />
      </radialGradient>
      <radialGradient
        cx="0"
        cy="0"
        r="1"
        gradientUnits="userSpaceOnUse"
        id="master_svg2_118_68087"
        gradientTransform="translate(66 20) rotate(90) scale(20.328431367874146 105.81049310283986)"
      >
        <stop
          offset="63.60992193222046%"
          stopColor="#FFFFFF"
          stopOpacity="0.05999999865889549"
        />
        <stop offset="99.99998807907104%" stopColor="#13275E" stopOpacity="0" />
      </radialGradient>
    </defs>
    <g>
      <g filter="url(#master_svg0_204_06335)">
        <rect
          x="0"
          y="0"
          width="132"
          height="40"
          rx="8"
          fill="#00102C"
          fillOpacity="0.699999988079071"
        />
        <rect
          x="0"
          y="0"
          width="132"
          height="40"
          rx="8"
          fill="#3C7EFF"
          fillOpacity="0.30000001192092896"
        />
        <rect
          x="0"
          y="0"
          width="132"
          height="40"
          rx="8"
          fill="url(#master_svg1_200_18384)"
          fillOpacity="1"
        />
        <rect
          x="0.5"
          y="0.5"
          width="131"
          height="39"
          rx="7.5"
          fillOpacity="0"
          strokeOpacity="1"
          stroke="#2ACAFF"
          fill="none"
          strokeWidth="1"
        />
      </g>
      <g>
        <path
          d="M1.0000000000000018,8.942879999999999L3.19641,2.90829Q3.85216,2.28964,4.64326,1.857337L1,11.8671L1,8.942879999999999L1.0000000000000018,8.942879999999999ZM1,20.6596L1,17.7353L7.06851,1.0622536Q7.53217,1.0000000000000009,8,1L8.1555,1L1,20.6596ZM1,29.4547L1,26.5305L10.29234,1.0000000000000018L11.3567,1L0.9999999999999982,29.4547L1,29.4547ZM1.976683,35.5665Q1.613307,34.9528,1.381168,34.2784L13.4935,1.0000000000000018L14.5578,1L1.976683,35.5665ZM4.30993,37.9484Q3.86646,37.6733,3.46868,37.3355L16.6937,1.0000000000000018L17.758,1L4.30993,37.9484ZM7.14638,38.9478Q6.64274,38.8859,6.15329,38.752L19.8939,1.0000000000000018L20.9582,1L7.14638,38.9478ZM10.32756,39L9.26323,39L23.0941,1.0000000000000018L24.1584,1L10.32756,39ZM13.5287,39L12.4644,39L26.2953,1.0000000000000018L27.3596,1L13.5287,39ZM16.7289,39L15.6646,39L29.4955,1.0000000000000018L30.5598,1L16.7289,39ZM19.9291,39L18.8648,39L32.6957,1.0000000000000018L33.76,1L19.9291,39ZM23.1303,39L22.066,39L35.8968,1.0000000000000018L36.9612,1L23.1303,39ZM26.3305,39L25.2662,39L39.097,1.0000000000000018L40.1614,1L26.3305,39ZM29.5307,39L28.4664,39L42.2972,1.0000000000000018L43.3616,1L29.5307,39ZM32.731899999999996,39L31.6675,39L45.4984,1.0000000000000018L46.5627,1L32.731899999999996,39ZM35.9321,39L34.8677,39L48.6986,1.0000000000000018L49.7629,1L35.9321,39ZM39.1332,39L38.0689,39L51.8998,1.0000000000000018L52.9641,1L39.1332,39ZM42.3334,39L41.2691,39L55.1,1.0000000000000018L56.1643,1L42.3334,39ZM45.5336,39L44.4693,39L58.3002,1.0000000000000018L59.3645,1L45.5336,39ZM48.7348,39L47.6705,39L61.5013,1.0000000000000018L62.5657,1L48.7348,39ZM51.935,39L50.8707,39L64.70150000000001,1.0000000000000018L65.7658,1L51.935,39ZM55.1352,39L54.0708,39L67.9017,1.0000000000000018L68.966,1L55.1352,39ZM58.3354,39L57.271,39L71.1019,1.0000000000000018L72.1662,1L58.3354,39ZM61.5365,39L60.4722,39L74.3031,1.0000000000000018L75.3674,1L61.5365,39ZM64.7367,39L63.6724,39L77.5033,1.0000000000000018L78.5676,1L64.7367,39ZM67.9369,39L66.8726,39L80.7035,1.0000000000000018L81.7678,1L67.9369,39ZM71.1381,39L70.0738,39L83.9046,1.0000000000000018L84.969,1L71.1381,39ZM74.3383,39L73.274,39L87.1048,1.0000000000000018L88.1692,1L74.3383,39ZM77.5395,39L76.4751,39L90.306,1.0000000000000018L91.3703,1L77.5395,39ZM80.7397,39L79.6753,39L93.5062,1.0000000000000018L94.5705,1L80.7397,39ZM83.9399,39L82.8755,39L96.7064,1.0000000000000018L97.7707,1L83.9399,39ZM87.141,39L86.0767,39L99.9076,1.0000000000000018L100.9719,1L87.141,39ZM90.3412,39L89.2769,39L103.108,1.0000000000000018L104.172,1L90.3412,39ZM93.5414,39L92.4771,39L106.308,1.0000000000000018L107.372,1L93.5414,39ZM96.7426,39L95.6783,39L109.509,1.0000000000000018L110.573,1L96.7426,39ZM99.9428,39L98.8785,39L112.709,1.0000000000000018L113.774,1L99.9428,39ZM103.143,39L102.079,39L115.91,1.0000000000000018L116.974,1L103.143,39ZM106.343,39L105.279,39L119.11,1.0000000000000018L120.174,1L106.343,39ZM109.544,39L108.48,39L122.311,1.0000000000000018L123.375,1L109.544,39ZM112.746,39L111.681,39L125.456,1.153166Q125.948,1.257713,126.419,1.431396L112.746,39ZM115.946,39L114.881,39L128.202,2.4015899999999997Q128.63299999999998,2.72473,129.009,3.10988L115.946,39ZM119.146,39L118.082,39L130.398,5.16042Q130.737,5.9229,130.886,6.74355L119.146,39ZM122.346,39L121.282,39L131,12.2994L131,15.2237L122.346,39ZM125.616,38.8109Q125.06,38.9429,124.489,38.9829L131,21.0946L131,24.0188L125.616,38.8109ZM129.958,35.6751Q129.611,36.2373,129.165,36.7247Q128.719,37.2121,128.19,37.6075L131,29.887L131,32Q131,32.5098,130.926,33.0142L129.958,35.6751Z"
          fillRule="evenodd"
          fill="url(#master_svg2_118_68087)"
          fillOpacity="1"
        />
      </g>
    </g>
  </svg>
);
type Props = {
  className?: string;
  onClick?: (visible: boolean) => void;
};

const HeatButton = (props: Props) => {
  const [checked, { toggle }] = useBoolean(false);
  const onClick = () => {
    toggle();
  };

  useUpdateEffect(() => {
    props.onClick?.(checked);
  }, [checked]);

  return (
    <div onClick={onClick} className={clsx(styles.heatButton, props.className)}>
      {bgSvg}
      <div className="absolute w-full h-full top-0 left-0 flex justify-center items-center">
        <span>
          <Checkbox checked={checked}></Checkbox>
        </span>
        <span>
          <img className="align-bottom mx-1" src={HeatIcon} alt="" />
        </span>
        <span className={styles.buttonFont}>热力图</span>
      </div>
    </div>
  );
};

export default HeatButton;
