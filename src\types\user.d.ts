namespace User {
  export interface LoginParams {
    password: string;
    username: string;
  }
  export interface ISimpleUser {
    id: number;
    imageBasePath: string;
    path: null;
    roles_code: number;
    roles_status: number;
    rules: string;
    status: number;
    token: string;
    username: string;
  }
  export interface IUserInfo {
    create_time: string;
    department: string;
    email: string;
    employment: string;
    id: number;
    last_login_time: string;
    learning_duration: number;
    learning_duration_month: number;
    name: string;
    phone: string;
    roles: string;
    seniority: number;
    status: number;
    username: string;
  }
  export interface IUserDetailResponse {
    data: IUserInfo;
    imageBasePath: string;
  }
}
