import { CustomSelect } from "@/pages/Contrast/CustomSelect";
import { tableData, treeData } from "@/utils/mockData";
import {
  DownloadOutlined,
  EyeOutlined,
  SearchOutlined,
  UploadOutlined,
} from "@ant-design/icons";
import { Button, ConfigProvider, Table, TimePicker, Tree } from "antd";
import type { DataNode, TreeProps } from "antd/es/tree";
import React, { useEffect, useState } from "react";
import styles from "./index.module.scss";
// 解构RangePicker组件
const { RangePicker } = TimePicker;

type RangePickerProps = React.ComponentProps<typeof RangePicker>;

// 定义数据结构类型
interface TableDataItem {
  id: string;
  treeNodeId: string; // 关联树节点ID
  sceneName: string;
  fileNo: string;
  standardNo: string;
  level: string;
  regulatoryLevel: string;
  status: string | string | string;
  daysToExpire?: number;
  publishDate: string;
  effectiveDate: string;
  expiryDate: string;
}

// 定义树节点数据类型
interface TreeNode extends DataNode {
  id: string;
  count?: number; // 节点数据计数
}

const TreeTable: React.FC = () => {
  const [select1Open, setSelect1Open] = useState<boolean>(false);
  const [timeRange, setTimeRange] = useState<RangePickerProps["value"]>(null);

  // 树结构相关状态
  const [treeDataList, setTreeData] = useState<TreeNode[]>([]);
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const [selectedKeys, setSelectedKeys] = useState<React.Key[]>([]);

  // 表格数据相关状态
  const [allTableData, setAllTableData] = useState<TableDataItem[]>([]);
  const [filteredTableData, setFilteredTableData] = useState<TableDataItem[]>(
    []
  );

  // 状态颜色映射
  useEffect(() => {
    setTreeData(treeData);
    setExpandedKeys(["all", "group", "line", "line1"]);
    setSelectedKeys(["all"]);
  }, []);

  useEffect(() => {
    setAllTableData(tableData);
    setFilteredTableData(tableData);
  }, []);

  const onTreeSelect: TreeProps["onSelect"] = (selectedKeys, { node }) => {
    setSelectedKeys(selectedKeys);
    if (selectedKeys.length === 0) return;
    const nodeKey = selectedKeys[0] as string;
    if (nodeKey === "all") {
      setFilteredTableData(allTableData); // 显示所有数据
      return;
    }
    if (nodeKey === "group") {
      setFilteredTableData(
        allTableData.filter((item) => item.level === "集团级")
      );
      return;
    }
    if (nodeKey === "line") {
      setFilteredTableData(
        allTableData.filter((item) => item.level === "线路级")
      );
      return;
    }
    if (nodeKey === "center") {
      // 中心级应显示其下所有子节点数据
      setFilteredTableData(
        allTableData.filter((item) => item.treeNodeId === "center-sub1")
      );
      return;
    }
    if (nodeKey === "line1") {
      setFilteredTableData(
        allTableData.filter(
          (item) =>
            item.treeNodeId === "line1-sub1" || item.treeNodeId === "line1-sub2"
        )
      );
      return;
    }
    if (nodeKey === "line2") {
      // 2号线应显示其下所有子节点数据
      setFilteredTableData(
        allTableData.filter(
          (item) =>
            item.treeNodeId === "line2" || item.treeNodeId === "line2-sub1"
        )
      );
      return;
    }
    setFilteredTableData(
      allTableData.filter((item) => item.treeNodeId === nodeKey)
    );
  };
  const onTreeExpand: TreeProps["onExpand"] = (expandedKeys) => {
    setExpandedKeys(expandedKeys);
  };
  return (
    <>
      <div className={styles.tree}>
        <ConfigProvider
          theme={{
            token: {
              colorBgBase: "rgba(41,202,255,0.2)",
              colorTextBase: "#eaf9ff",
            },
            components: {
              Tree: {
                nodeSelectedColor: "#29CAFF",
                nodeSelectedBg: "rgba(41,202,255,0.2)",
                titleHeight: 40,
                indentSize: 40,
              },
            },
          }}
        >
          <Tree
            className={styles.trees}
            treeData={treeDataList}
            expandedKeys={expandedKeys}
            selectedKeys={selectedKeys}
            onSelect={onTreeSelect}
            onExpand={onTreeExpand}
            showLine
            blockNode
          />
        </ConfigProvider>
      </div>
      <div className={styles.table}>
        <div className={styles.header}>
          <div className="w-[70%] h-full flex justify-evenly items-center">
            <div>
              项目状态：
              <CustomSelect
                defaultValue="all"
                style={{ width: 180 }}
                onOpenChange={(open) => setSelect1Open(open)}
                selectOpen={select1Open}
                options={[
                  { value: "all", label: "全部" },
                  { value: "valid", label: "有效" },
                  { value: "expiring", label: "即将失效" },
                  { value: "expired", label: "已失效" },
                ]}
              />
            </div>
            <div>
              规则类型：
              <CustomSelect
                defaultValue="all"
                style={{ width: 180 }}
                onOpenChange={(open) => setSelect1Open(open)}
                selectOpen={select1Open}
                options={[
                  { value: "all", label: "全部" },
                  { value: "document", label: "红头文件" },
                  { value: "technical", label: "技术规范" },
                  { value: "service", label: "服务标准" },
                  { value: "production", label: "生产组织类文件" },
                ]}
              />
            </div>
            <div>
              实现日期：
              <RangePicker
                value={timeRange}
                onChange={setTimeRange}
                style={{ width: 260 }}
                format="YYYY-MM-DD"
                placeholder={["开始时间", "结束时间"]}
              />
            </div>
          </div>
          <div className="w-[30%] h-full flex justify-evenly items-center">
            <div className={styles.searchWrapper}>
              <input
                type="text"
                placeholder="输入搜索关键词"
                className={styles.searchInput}
              />
              <button className={styles.searchButtonInside}>
                <SearchOutlined style={{ fontSize: "18px" }} />
              </button>
            </div>
            <Button icon={<UploadOutlined />} className={styles.importButton}>
              导入
            </Button>
          </div>
        </div>
        <div className={styles.tableBody}>
          <ConfigProvider
            theme={{
              token: {
                colorBgBase: "rgba(116, 162, 255, 0)",
                colorTextBase: "#eaf9ff",
              },
              components: {
                Table: {
                  colorText: "#DFEAFF ",
                  headerColor: "#DFEAFF",
                  rowHoverBg: "rgba(60,126,255,0.2)",
                  headerBg:
                    "linear-gradient( 180deg, rgba(116,163,255,0.15) 0%, rgba(116,163,255,0.05) 100%);",
                  borderColor: "black",
                  cellPaddingBlock: 16,
                  cellPaddingInline: 10,
                },
                Button: {
                  paddingInline: 6,
                },
              },
            }}
          >
            <Table
              dataSource={filteredTableData}
              rowKey="id"
              columns={[
                {
                  title: "规章名称",
                  dataIndex: "sceneName",
                  key: "sceneName",
                  width: 200,
                },
                {
                  title: "文件号",
                  dataIndex: "fileNo",
                  key: "fileNo",
                  width: 150,
                },
                {
                  title: "标准号",
                  dataIndex: "standardNo",
                  key: "standardNo",
                  width: 150,
                },
                {
                  title: "级别",
                  dataIndex: "level",
                  key: "level",
                  width: 100,
                },
                {
                  title: "规章类型",
                  dataIndex: "regulatoryLevel",
                  key: "regulatoryLevel",
                  width: 150,
                },
                {
                  title: "状态",
                  dataIndex: "status",
                  key: "status",
                  render: (text) => {
                    let statusClass = "";
                    if (text === "有效") {
                      statusClass = styles.statusFour;
                    } else if (text === "已失效") {
                      statusClass = styles.statusThree;
                    } else if (text.includes("小时")) {
                      statusClass = styles.statusTwo;
                    } else if (text.includes("天后")) {
                      statusClass = styles.statusOne;
                    }
                    return <Button className={statusClass}>{text}</Button>;
                  },
                  width: 150,
                },
                {
                  title: "发布日期",
                  dataIndex: "publishDate",
                  key: "publishDate",
                  width: 120,
                },
                {
                  title: "操作",
                  dataIndex: "actions",
                  key: "operations",
                  render: (actions) => (
                    <div className="flex justify-between">
                      <Button
                        icon={<EyeOutlined />}
                        className="text-[#29C4FB] hover:text-blue-800 border-none"
                      >
                        查看
                      </Button>
                      <Button
                        icon={<DownloadOutlined />}
                        className="text-[#29C4FB] hover:text-blue-800 border-none"
                      >
                        下载
                      </Button>
                    </div>
                  ),
                  width: 120,
                },
              ]}
              pagination={{
                pageSize: 10,
                pageSizeOptions: ["10", "20", "30"],
                showSizeChanger: true,
              }}
              scroll={{ y: "calc(100vh - 500px)" }}
              rowClassName={(record, index) =>
                index % 2 === 0 ? styles["even-row"] : styles["odd-row"]
              }
            />
          </ConfigProvider>
        </div>
      </div>
    </>
  );
};

export default TreeTable;
