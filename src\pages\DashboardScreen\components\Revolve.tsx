import React, { useState, useRef, useEffect } from "react";
import { ConfigProvider, Select } from "antd";
import styles from "../index.module.scss";
import { IMAGES } from "../images/index";
import { darkTheme } from "../../../hooks/useTheme";
const { Option } = Select;
const Revolve: React.FC = () => {
  const [hoveredId, setHoveredId] = useState<number | null>(null);
  const [selectedLine, setSelectedLine] = useState<string>("all");
  console.log(hoveredId, selectedLine);
  // 添加旋转相关状态
  const [isRotating, setIsRotating] = useState<boolean>(false);
  const [rotationProgress, setRotationProgress] = useState<number>(0);
  const [targetId, setTargetId] = useState<number | null>(null);
  // 添加选中状态
  const [selectedId, setSelectedId] = useState<number | null>(null);
  const centerRef = useRef<HTMLDivElement>(null);

  // 添加缩放动画状态
  const [scaleProgress, setScaleProgress] = useState<number>(0);

  // 线路选择处理函数
  const handleLineSelect = (value: string) => {
    setSelectedLine(value);
  };

  const individualItems = [
    { id: 1, image: IMAGES.safe, number: 14, title: "信息传递不到位" },
    { id: 2, image: IMAGES.ats, number: 14, title: "运营调整不合理" },
    { id: 3, image: IMAGES.operation, number: 14, title: "协调联动不足" },
    { id: 4, image: IMAGES.time, number: 14, title: "命令发布不规范" },
    { id: 5, image: IMAGES.information, number: 14, title: "安全把控不到位" },
    { id: 6, image: IMAGES.command, number: 14, title: "时间点把控不规范" },
    { id: 7, image: IMAGES.harmonize, number: 14, title: "ATS操作不规范" },
  ];

  const [initialAngles, setInitialAngles] = useState<Record<number, number>>({
    1: 0,
    2: 51,
    3: 103,
    4: 154,
    5: 206,
    6: 257,
    7: 309,
  });

  // 处理选中
  const handleItemClick = (id: number) => {
    setSelectedId(id);

    if (isRotating) return;

    setTargetId(id);
    setIsRotating(true);
    setRotationProgress(0);
    // 重置缩放进度
    setScaleProgress(0);

    // 持续的时间
    const duration = 2000;
    const startTime = Date.now();

    const animate = () => {
      const currentTime = Date.now();
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // 过度效果
      const easeProgress = 1 - Math.pow(1 - progress, 3);
      setRotationProgress(easeProgress);
      // 更新缩放进度
      setScaleProgress(easeProgress);

      if (progress < 1) {
        requestAnimationFrame(animate);
      } else {
        setIsRotating(false);
        // 重置初始角度为90度
        const targetItem = individualItems.find((item) => item.id === id);
        if (targetItem) {
          // 获取当前个体的初始角度
          const targetInitialAngle = initialAngles[id];
          // 角度差
          const angleDiff = (90 - targetInitialAngle + 360) % 360;

          // 创建新对象
          const newInitialAngles = { ...initialAngles };
          Object.keys(newInitialAngles).forEach((key) => {
            const itemId = parseInt(key);
            newInitialAngles[itemId] =
              (newInitialAngles[itemId] + angleDiff) % 360;
          });

          // 更新状态
          setInitialAngles(newInitialAngles);
        }
        setRotationProgress(0);
        // 确保缩放进度完成
        setScaleProgress(1);
      }
    };

    requestAnimationFrame(animate);
  };

  // 计算每个项的位置
  const calculatePosition = (itemId: number) => {
    const centerX = 50;
    const centerY = 50;
    const radiusX = 40;
    const radiusY = 25;

    // 基础角度
    let baseAngle = initialAngles[itemId];

    if (isRotating && targetId) {
      const targetInitialAngle = initialAngles[targetId];
      const totalRotation = (90 - targetInitialAngle + 360) % 360;
      const currentRotation = totalRotation * rotationProgress;
      baseAngle = (baseAngle + currentRotation) % 360;
    }

    // 转换为弧度
    const radians = (baseAngle * Math.PI) / 180;

    // 计算X和Y坐标
    const x = centerX + radiusX * Math.cos(radians);
    const y = centerY + radiusY * Math.sin(radians);

    // 计算缩放比例 - 基于角度与90度的距离
    const angleToFront = Math.abs((baseAngle - 90 + 360) % 360);
    const halfDistance = Math.min(angleToFront, 360 - angleToFront);
    // 缩放比例
    const scale = 1.2 - (halfDistance / 180) * 0.7;
    // 如果是选中项，在旋转过程中逐渐放大
    let finalScale = scale;
    if (selectedId === itemId) {
      // 基础缩放比例 + 动画增量
      const scaleIncrement = 0.3 * scaleProgress;
      finalScale = scale + scaleIncrement;
    }

    return {
      x: `${x}%`,
      y: `${y}%`,
      transform: `translate(-50%, -50%) scale(${finalScale})`,
      scale: finalScale,
    };
  };
  useEffect(() => {
    setSelectedId(1);
    handleItemClick(1);
  }, []);
  return (
    <ConfigProvider theme={darkTheme}>
      <div className={styles.revolve}>
        <div className={styles.sift}>
          <Select
            defaultValue="all"
            style={{ width: 120 }}
            onChange={handleLineSelect}
          >
            <Option value="all">全部线路</Option>
            <Option value="fullStack">全栈线</Option>
            <Option value="eighteenth">十八号线</Option>
          </Select>
        </div>
        {/* 圆桌 */}
        <div
          className={styles.tipImage}
          title=""
          style={{ display: isRotating ? "none" : "block" }}
        >
          <div className={styles.tipText}>
            测试测试测试测测试测试测试测测试测试测试测
          </div>
        </div>
        <div className={styles.centerTable} ref={centerRef}>
          <img
            src={IMAGES.base}
            alt="中心椭圆桌"
            className={styles.centerImage}
          />
        </div>
        {/* 各个小块 */}
        {individualItems.map((item) => {
          const position = calculatePosition(item.id);
          return (
            <div
              key={item.id}
              className={styles.individualBase}
              style={{
                backgroundImage:
                  selectedId === item.id
                    ? `url(${IMAGES.selected})`
                    : `url(${IMAGES.notSelected})`,
                left: position.x,
                top: position.y,
                transform: position.transform,
                position: "absolute",
                transition: "none",
                zIndex: Math.floor(position.scale * 10),
                // 新增禁用功能
                pointerEvents: isRotating ? "none" : "auto",
                cursor: isRotating ? "not-allowed" : "pointer",
              }}
              onMouseEnter={() => setHoveredId(item.id)}
              onMouseLeave={() => setHoveredId(null)}
              onClick={() => handleItemClick(item.id)}
            >
              <div className="imgDiv">
                <img src={item.image} alt="" />
              </div>
              <div className="individual-number">{item.number}</div>
              <div className="individual-title">{item.title}</div>
            </div>
          );
        })}
      </div>
    </ConfigProvider>
  );
};

export default Revolve;
