.overlay {
  position: absolute;
  left: 20px;
  bottom: 20px;
  width: calc(100vw - var(--panel-width) - 20px * 3);
  z-index: 800;
  .contentBox {
    height: 52px;
    background: linear-gradient(
      270deg,
      rgba(24, 45, 99, 0.4) 0%,
      rgba(19, 39, 94, 0.1) 98%
    );
    border-radius: 8px 8px 8px 8px;
    border: 1px solid rgba(2, 93, 244, 0.2);
    --at-apply: flex-center pt-1;
  }
}

.scrollBox {
  width: calc(100% - 80px);
  overflow: hidden;
  .scrollContent {
    white-space: nowrap;
  }
}

.lineButton {
  display: inline-flex;
  width: 88px;
  height: 36px;
  margin-right: 6px;
  position: relative;
  color: rgba(255, 255, 255, 0.8);
  font-size: 16px;

  &:last-child {
    margin-right: 0;
  }

  .lineContent {
    --at-apply: absolute left-0 top-0 flex-center w-full h-full cursor-pointer;
  }

  .lineColor {
    width: 20px;
    .colorBox {
      width: 16px;
      height: 6px;
      background: #8cc220;
    }
  }
}


@mixin arrow {
  --at-apply: absolute cursor-pointer hover:opacity-65 active:opacity-100;
}
.leftArrow {
  left: 14px;
  @include arrow;
}

.rightArrow {
  right: 14px;
  @include arrow;
  --at-apply: rotate-180;
}
