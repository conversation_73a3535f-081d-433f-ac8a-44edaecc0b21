import Navigation from "@/components/HerderNavigat";
import { useTheme } from "@/hooks/useTheme";
import { ConfigProvider, Layout } from "antd";
import { Outlet } from "react-router-dom";
import styles from "./index.module.scss";

export default function Subject() {
  const { darkTheme } = useTheme();

  return (
    <ConfigProvider theme={darkTheme}>
      <Layout className={styles.layout}>
        <Navigation />
        <Outlet />
      </Layout>
    </ConfigProvider>
  );
}
