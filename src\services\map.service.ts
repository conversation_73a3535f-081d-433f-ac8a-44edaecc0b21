import {
  HeatLatLngTuple,
  HeatMapOptions,
  LatLng,
  LatLngExpression,
  Map,
  Proj,
  TileLayer,
  heatLayer,
  map,
  tileLayer,
  type MapOptions,
} from "leaflet";
import "leaflet.heat";
import "proj4leaflet";

const mapTileUrl = import.meta.env.VITE_MAP_URL;

const baseCenterPoint: LatLngExpression = [31.18, 121.36];
// const baseCenterPoint: LatLngExpression = [31.075, 121.63];
// 31.05423, 121.629639
const centerOffset = [-0.01, 0.24];
const autoplayOffset = [-0.05, 0.15];
const centerPoint: LatLngExpression = [
  baseCenterPoint[0] + centerOffset[0],
  baseCenterPoint[1] + centerOffset[1],
];

// const boundary: number[][] = [
//   [31.404419, 121.234131],
//   [31.414032, 121.335754],
//   [31.418152, 121.423645],
//   [31.418152, 121.474457],
//   [31.390686, 121.525269],
//   [31.357727, 121.591187],
//   [31.320648, 121.618652],
//   [31.278076, 121.68457],
//   [31.209412, 121.777954],
//   [31.153107, 121.80954],
//   [30.904541, 121.933136],
//   [30.901794, 121.49231],
//   [30.927887, 121.216278],
//   [31.099548, 121.003418],
//   [31.301422, 121.085815],
//   [31.404419, 121.234131],
// ];
// const boundaryPolygon = polygon([boundary]);

export class MapService {
  map?: Map;
  options: MapOptions = {};
  baseLayers: TileLayer[] = [];
  heatLayer = heatLayer([], this.getHeatMapConfig());
  constructor() {}

  getLeafletOptions() {
    const leafletOptions: MapOptions = {
      crs: new Proj.CRS("EPSG:4490", "+proj=longlat +ellps=GRS80 +no_defs", {
        resolutions: [
          1.40625, 0.703125, 0.3515625, 0.17578125, 0.087890625, 0.0439453125,
          0.02197265625, 0.010986328125, 0.0054931640625, 0.00274658203125,
          0.001373291015625, 6.866455078125e-4, 3.4332275390625e-4,
          1.71661376953125e-4, 8.58306884765625e-5, 4.291534423828125e-5,
          2.1457672119140625e-5, 1.0728836059570312e-5, 5.364418029785156e-6,
          2.682209064925356e-6, 1.3411045324626732e-6,
        ],
        origin: [-180, 90],
      }),
      center: centerPoint,
      zoom: 11,
      minZoom: 9,
      maxZoom: 14,
      zoomControl: false,
      maxBounds: [
        [32.10396, 124.260378],
        [30.014771, 119.99949],
      ],
      maxBoundsViscosity: 1,
    };
    return leafletOptions;
  }
  disableMoveAndZoom() {
    this.map?.scrollWheelZoom.disable();
    this.map?.dragging.disable();
  }

  followCoord(coord: number[], zoom: number = 11) {
    if (!this.map) {
      return;
    }
    const curZoom = this.map.getZoom();
    if (curZoom !== zoom) {
      setTimeout(() => {
        this.map!.setZoom(zoom);
      }, 300);
    }
    setTimeout(() => {
      this.map!.panTo([
        coord[0] + autoplayOffset[0],
        coord[1] + autoplayOffset[1],
      ] as LatLngExpression);
    }, 600);
  }
  viewCoord(coord: number[], zoom: number = 11) {
    if (!this.map) {
      return;
    }
    const curZoom = this.map.getZoom();
    if (curZoom !== zoom) {
      setTimeout(() => {
        this.map!.setZoom(zoom);
      }, 300);
    }
    setTimeout(() => {
      this.map!.panTo([coord[0], coord[1]] as LatLngExpression);
    }, 600);
  }
  enableMoveAndZoom() {
    this.map?.scrollWheelZoom.enable();
    this.map?.dragging.enable();
  }
  getBaseLayers(): Record<string, TileLayer> {
    const url = `${mapTileUrl}/vec/{z}/{y}/{x}.png`;
    const url_c = `${mapTileUrl}/cva/{z}/{y}/{x}.png`;
    const vec = tileLayer(url, {
      // tileSize: 1024,
      detectRetina: true,
    });
    return {
      vec,
      cva: tileLayer(url_c, {
        // tileSize: 1024,
        detectRetina: true,
      }),
    };
  }
  getMap(dom: HTMLDivElement) {
    this.options = this.getLeafletOptions();
    const { vec, cva } = this.getBaseLayers();
    this.baseLayers = [vec, cva];
    this.map = map(dom, {
      ...this.options,
      layers: [vec, cva],
    });
    return this.map;
  }
  getMapWithOptions(dom: HTMLDivElement, options: MapOptions) {
    this.options = this.getLeafletOptions();
    const { vec, cva } = this.getBaseLayers();
    this.baseLayers = [vec, cva];
    this.map = map(dom, {
      ...this.options,
      ...options,
      layers: [vec, cva],
    });
    return this.map;
  }

  getHeatMapConfig() {
    const cfg: HeatMapOptions = {
      radius: 15,
      max: 1,
      minOpacity: 0,
      gradient: {
        0.3: "green",
        0.65: "yellow",
        1: "red",
      },
    };
    return cfg;
  }

  showHeatMap(data: Array<LatLng | HeatLatLngTuple>) {
    mapService.heatLayer.setLatLngs(data);
    mapService.heatLayer.addTo(this.map!);
  }

  removeHeatMap() {
    this.heatLayer.setLatLngs([]);
    setTimeout(() => {
      this.heatLayer.remove();
    });
  }
}

export const mapService = new MapService();
