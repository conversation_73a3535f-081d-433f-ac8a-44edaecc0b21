import { User<PERSON><PERSON> } from "@/apis/user";
import customStorage from "@/utils/storage";
import { LockOutlined, UserOutlined } from "@ant-design/icons";
import { useRequest } from "ahooks";
import { Button, Card, Form, Input, message, Typography } from "antd";
import CryptoJ<PERSON> from "crypto-js";
import React from "react";

const { Title } = Typography;

const Login: React.FC = () => {
  const [messageApi, contextHolder] = message.useMessage();
  const { run: login } = useRequest(UserApi.login, {
    manual: true,
    onSuccess(res) {
      localStorage.setItem("token", res.token);
      customStorage.set!("token", res.token, 0);
      customStorage.set!("userId", res.id, 0);
      messageApi.success("登录成功");
      setTimeout(() => {
        window.location.href = "/fe/portal";
      }, 1000);
    },
    onError(err) {
      messageApi.error(err.message);
    },
  });

  const onFinish = (values: User.LoginParams) => {
    login({
      username: values.username,
      password: CryptoJS.MD5(values.password).toString(),
    });
  };

  const onFinishFailed = (errorInfo: any) => {
    console.log("Failed:", errorInfo);
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50">
      {contextHolder}
      <Card className="w-full max-w-md p-6 shadow-lg">
        <div className="text-center mb-6">
          <Title level={2}>系统登录(仅开发用)</Title>
        </div>
        <Form
          name="login_form"
          initialValues={{ remember: true }}
          onFinish={onFinish}
          onFinishFailed={onFinishFailed}
          autoComplete="off"
        >
          <Form.Item
            name="username"
            rules={[{ required: true, message: "请输入用户名!" }]}
          >
            <Input
              prefix={<UserOutlined className="site-form-item-icon" />}
              placeholder="用户名"
            />
          </Form.Item>

          <Form.Item
            name="password"
            rules={[{ required: true, message: "请输入密码!" }]}
          >
            <Input
              prefix={<LockOutlined className="site-form-item-icon" />}
              type="password"
              placeholder="密码"
            />
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              className="w-full"
              size="large"
            >
              登录
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default Login;
