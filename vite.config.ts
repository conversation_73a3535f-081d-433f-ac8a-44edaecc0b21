import react from "@vitejs/plugin-react";
import path from "path";
import UnoCSS from "unocss/vite";
import { fileURLToPath } from "url";
import { defineConfig } from "vite";
import svgr from "vite-plugin-svgr";

// 解决__dirname在ES模块中的使用问题
const __dirname = path.dirname(fileURLToPath(import.meta.url));

export default defineConfig({
  // 使用React和UnoCSS插件
  plugins: [react(), UnoCSS(), svgr()],
  envDir: "env",
  resolve: {
    alias: {
      "@": path.join(__dirname, "src"),
    },
  },
  base: "/fe",
  css: {
    modules: {
      generateScopedName: "[name]__[local]__[hash:base64:5]",
    },
    preprocessorOptions: {
      scss: {
        additionalData: `@use '@/assets/style/index.scss';`,
      },
    },
  },
  build: {
    minify: "esbuild",
    rollupOptions: {
      output: {
        chunkFileNames: "assets/js/[name]-[hash].js",
      },
    },
    cssMinify: true,
  },
  server: {
    port: 3000,
    open: true,
    cors: true,
    proxy: {
      "/api": {
        target: "https://metro2.stage.leadinvr.com/",
        changeOrigin: true,
        // rewrite: (path) => {
        //   console.log("~~~", path);
        //   return path.replace(/^\/api/, "");
        // },
      },
    },
  },
});
