interface TreeNode {
  id: string;
  key: string;
  title: string;
  children?: TreeNode[];
  count?: number;
}

interface TableDataItem {
  id: string;
  treeNodeId: string;
  sceneName: string;
  fileNo: string;
  standardNo: string;
  level: string;
  regulatoryLevel: string;
  status: string | string | string;
  daysToExpire?: number;
  publishDate: string;
  effectiveDate: string;
  expiryDate: string;
}

export const treeData: TreeNode[] = [
  {
    id: "all",
    key: "all",
    title: "全部 (14)",
    children: [
      {
        id: "group",
        key: "group",
        title: "集团级 (5)",
        children: [
          {
            id: "sub1",
            key: "sub1",
            title: "子类1 (3)",
          },
          {
            id: "sub2",
            key: "sub2",
            title: "子类2 (2)",
          },
        ],
      },
      {
        id: "center",
        key: "center",
        title: "中心级 (2)",
        children: [
          {
            id: "center-sub1",
            key: "center-sub1",
            title: "中心子类1 (2)",
          },
        ],
      },
      {
        id: "line",
        key: "line",
        title: "线路级 (5)",
        children: [
          {
            id: "line1",
            key: "line1",
            title: "1号线 (2)",
            children: [
              {
                id: "line1-sub1",
                key: "line1-sub1",
                title: "子类1 (1)",
              },
              {
                id: "line1-sub2",
                key: "line1-sub2",
                title: "子类2 (1)",
              },
            ],
          },
          {
            id: "line2",
            key: "line2",
            title: "2号线 (5)",
            children: [
              {
                id: "line2-sub1",
                key: "line2-sub1",
                title: "子类1 (5)",
              },
            ],
          },
        ],
      },
    ],
  },
];

export const tableData: TableDataItem[] = [
  {
    id: "G1-001",
    treeNodeId: "sub1",
    sceneName: "运营调度管理规程",
    fileNo: "沪地铁运(2020)277号",
    standardNo: "Q/SD-YG-FT-Y-1001-2020",
    level: "集团级",
    regulatoryLevel: "红头文件",
    status: "3天后失效",
    daysToExpire: 3,
    publishDate: "2020-05-12",
    effectiveDate: "2020-06-01",
    expiryDate: "2023-08-11",
  },
  {
    id: "G1-002",
    treeNodeId: "sub1",
    sceneName: "设备维护技术标准",
    fileNo: "沪地铁技(2019)412号",
    standardNo: "Q/SD-YG-JS-FT-2019-088",
    level: "集团级",
    regulatoryLevel: "技术规范",
    status: "有效",
    publishDate: "2019-11-20",
    effectiveDate: "2020-01-01",
    expiryDate: "2025-12-31",
  },
  {
    id: "G1-003",
    treeNodeId: "sub1",
    sceneName: "安全生产管理条例",
    fileNo: "沪地铁安(2021)135号",
    standardNo: "Q/SD-YG-AQ-FT-2021-035",
    level: "集团级",
    regulatoryLevel: "生产组织类文件",
    status: "已失效",
    publishDate: "2021-03-15",
    effectiveDate: "2021-04-01",
    expiryDate: "2022-12-31",
  },

  {
    id: "G2-001",
    treeNodeId: "sub2",
    sceneName: "票务管理规定",
    fileNo: "沪地铁票(2018)335号",
    standardNo: "Q/SD-YG-PW-FT-2018-102",
    level: "集团级",
    regulatoryLevel: "红头文件",
    status: "有效",
    publishDate: "2018-09-10",
    effectiveDate: "2019-01-01",
    expiryDate: "2024-12-31",
  },
  {
    id: "G2-002",
    treeNodeId: "sub2",
    sceneName: "员工行为规范",
    fileNo: "沪地铁人(2022)078号",
    standardNo: "Q/SD-YG-XW-FT-2022-015",
    level: "集团级",
    regulatoryLevel: "服务标准",
    status: "20小时后失效",
    daysToExpire: 7,
    publishDate: "2022-02-18",
    effectiveDate: "2022-03-01",
    expiryDate: "2023-08-15",
  },
  {
    id: "C1-001",
    treeNodeId: "center-sub1",
    sceneName: "中心安全管理规定",
    fileNo: "沪地铁中(2022)011号",
    standardNo: "Q/SD-YG-AQ-ZX-2022-001",
    level: "中心级",
    regulatoryLevel: "红头文件",
    status: "有效",
    publishDate: "2022-05-20",
    effectiveDate: "2022-06-01",
    expiryDate: "2027-05-31",
  },
  {
    id: "C1-002",
    treeNodeId: "center-sub1",
    sceneName: "中心设备操作规程",
    fileNo: "沪地铁中(2021)033号",
    standardNo: "Q/SD-YG-CZ-ZX-2021-005",
    level: "中心级",
    regulatoryLevel: "技术规范",
    status: "1天后失效",
    daysToExpire: 20,
    publishDate: "2021-09-15",
    effectiveDate: "2021-10-01",
    expiryDate: "2023-09-10",
  },
  {
    id: "L1A-001",
    treeNodeId: "line1-sub1",
    sceneName: "1号线列车运行图",
    fileNo: "沪地铁1线(2021)022号",
    standardNo: "Q/SD-YG-YX-FT-2021-008",
    level: "线路级",
    regulatoryLevel: "技术规范",
    status: "有效",
    publishDate: "2021-04-10",
    effectiveDate: "2021-05-01",
    expiryDate: "2026-04-30",
  },

  {
    id: "L1B-001",
    treeNodeId: "line1-sub2",
    sceneName: "1号线车站服务指南",
    fileNo: "沪地铁1线(2022)033号",
    standardNo: "Q/SD-YG-FW-FT-2022-012",
    level: "线路级",
    regulatoryLevel: "服务标准",
    status: "有效",
    publishDate: "2022-03-15",
    effectiveDate: "2022-04-01",
    expiryDate: "2027-03-31",
  },

  {
    id: "L2-001",
    treeNodeId: "line2-sub1",

    sceneName: "2号线施工管理办法",
    fileNo: "沪地铁2线(2020)044号",
    standardNo: "Q/SD-YG-SG-FT-2020-025",
    level: "线路级",
    regulatoryLevel: "红头文件",
    status: "6天后失效",
    daysToExpire: 15,
    publishDate: "2020-11-01",
    effectiveDate: "2021-01-01",
    expiryDate: "2023-09-01",
  },
  {
    id: "L2-002",
    treeNodeId: "line2-sub1",
    sceneName: "2号线设备维护规程",
    fileNo: "沪地铁2线(2019)055号",
    standardNo: "Q/SD-YG-WX-FT-2019-030",
    level: "线路级",
    regulatoryLevel: "技术规范",
    status: "有效",
    publishDate: "2019-08-22",
    effectiveDate: "2019-10-01",
    expiryDate: "2024-09-30",
  },
  {
    id: "L2-003",
    treeNodeId: "line2-sub1",
    sceneName: "2号线客运组织方案",
    fileNo: "沪地铁2线(2023)012号",
    standardNo: "Q/SD-YG-KY-FT-2023-003",
    level: "线路级",
    regulatoryLevel: "红头文件",
    status: "有效",
    publishDate: "2023-01-15",
    effectiveDate: "2023-02-01",
    expiryDate: "2028-01-31",
  },
  {
    id: "L2-004",
    treeNodeId: "line2-sub1",
    sceneName: "2号线应急预案",
    fileNo: "沪地铁2线(2022)078号",
    standardNo: "Q/SD-YG-YJ-FT-2022-018",
    level: "线路级",
    regulatoryLevel: "安全管理类文件",
    status: "8小时后失效",
    daysToExpire: 30,
    publishDate: "2022-06-10",
    effectiveDate: "2022-07-01",
    expiryDate: "2023-09-30",
  },
  {
    id: "L2-005",
    treeNodeId: "line2-sub1",
    sceneName: "2号线信号系统操作手册",
    fileNo: "沪地铁2线(2021)099号",
    standardNo: "Q/SD-YG-XH-FT-2021-022",
    level: "线路级",
    regulatoryLevel: "技术规范",
    status: "有效",
    publishDate: "2021-11-25",
    effectiveDate: "2022-01-01",
    expiryDate: "2026-12-31",
  },
];
