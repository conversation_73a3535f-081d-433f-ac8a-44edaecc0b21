const isDev = import.meta.env.DEV;

export function toLogin() {
  if (isDev) {
    window.location.replace("/fe/login");
  } else {
    window.location.replace("/#/login");
  }
}

export function getAnimationFrame() {
  let requestAnimationFrame =
    window.requestAnimationFrame ||
    // @ts-ignore
    window.webkitRequestAnimationFrame ||
    // @ts-ignore
    window.mozRequestAnimationFrame ||
    // @ts-ignore
    window.msRequestAnimationFrame;

  if (requestAnimationFrame) {
    return requestAnimationFrame;
  }

  if (!requestAnimationFrame) {
    // @ts-ignore
    requestAnimationFrame = (callback: FrameRequestCallback) => {
      return setTimeout(() => {
        callback(performance.now());
      }, 1000 / 60);
    };
    window.cancelAnimationFrame = clearTimeout;
  }
  return requestAnimationFrame;
}

// 深度比较函数（处理基本类型、数组和对象）
export function isEqual(a: any, b: any): boolean {
  if (a === b) return true;

  // 处理数组比较
  if (Array.isArray(a) && Array.isArray(b)) {
    return (
      a.length === b.length && a.every((val, index) => isEqual(val, b[index]))
    );
  }

  // 处理对象比较
  if (
    typeof a === "object" &&
    a !== null &&
    typeof b === "object" &&
    b !== null
  ) {
    const keysA = Object.keys(a);
    const keysB = Object.keys(b);

    if (keysA.length !== keysB.length) return false;

    return keysA.every((key) => isEqual(a[key], b[key]) && keysB.includes(key));
  }

  return false;
}

export function findAncestorWithAttribute(
  element: HTMLElement,
  prop: string
): HTMLElement | null {
  let currentElement: HTMLElement | null = element;
  while (currentElement) {
    if (currentElement.hasAttribute(prop)) {
      return currentElement;
    }
    currentElement = currentElement.parentElement;
  }

  return null;
}
