import axios, { AxiosInstance, AxiosRequestHeaders } from "axios";
import customStorage from "./storage";
// create an axios request
const request: AxiosInstance = axios.create({
  // baseURL: process.env.VUE_APP_BASE_API, // url = base url + request url
  withCredentials: true, // send cookies when cross-domain requests
  timeout: 10 * 1000, // request timeout，10秒
  // transformResponse: [
  //   (data) => {
  // Do whatever you want to transform the data
  //     return JSONbig.parse(data)
  //   }
  // ]
});

// request interceptor
request.interceptors.request.use(
  (config) => {
    if (config.url === undefined) {
      config.url = "";
    }
    // do something before request is sen

    // const path = config.url.split('?')[0]
    // let search = config.url.split('?')[1] || ''

    // 添加时间戳
    // search = '?_v=' + Date.now() + (search ? '&' + search : '')
    // config.url = path + search
    config.headers = config.headers || ({} as AxiosRequestHeaders);
    if (localStorage.getItem("token") && customStorage.get!("token")) {
      config.headers.token = localStorage.getItem("token") || "";
    }
    return config;
  },
  (error) => {
    // do something with request error
    // console.log(error) // for debug
    return Promise.reject(error);
  }
);

// response interceptor
request.interceptors.response.use(
  /**
   * If you want to get http information such as headers or status
   * Please return  response => response
   */

  /**
   * Determine the request status by custom code
   * Here is just an example
   * You can also judge the status by HTTP Status Code
   */
  (response) => {
    const res = response.data;
    if (!res || res.r !== 0) {
      if (res.msg.includes("用户身份失效")) {
        customStorage.remove("token");
        return Promise.reject(res);
      }
      if (res.msg) {
        return Promise.reject(new Error(res.msg));
      }
      return Promise.reject(res);
    }
    return res.data;
  },
  (error) => {
    return Promise.reject(error);
  }
);

export default request;
