import { defineConfig, transformerDirectives } from "unocss";

export default defineConfig({
  // UnoCSS配置内容
  content: {
    pipeline: {
      include: [
        // 默认
        /\.(vue|svelte|[jt]sx|mdx?|astro|elm|php|phtml|html)($|\?)/,
        // 包括 js/ts 文件
        "src/utils/map/**/*.{js,ts}",
      ],
      // 排除文件
      // exclude: []
    },
  },
  rules: [
    ["text-shadow", { textShadow: "0 2px 4px rgba(0,0,0,0.1)" }],
    ["card-shadow", { boxShadow: "0 4px 12px rgba(0,0,0,0.05)" }],
    ["content-auto", { "content-visibility": "auto" }],
  ],
  shortcuts: {
    btn: "px-4 py-2 rounded-md font-medium transition-colors",
    "btn-primary": "bg-blue-600 text-white hover:bg-blue-700",
    "btn-secondary": "bg-gray-100 text-gray-800 hover:bg-gray-200",
    "flex-center": "flex items-center justify-center",
    container: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",
  },
  transformers: [transformerDirectives()],
});
