import undeveloped from "@/assets/images/home/<USER>";
import React from "react";
import styles from "../index.module.scss";
const PieChart: React.FC = () => {
  return (
    <div className={styles.pieChart}>
      <div className={styles.chartTitle}>
        <span>重大案例统计</span>
      </div>
      <div className={styles.chartBackground}>
        <div style={{ textAlign: "center" }}>
          <img
            src={undeveloped}
            style={{
              maxWidth: "80%",
              maxHeight: "200px",
              objectFit: "contain",
            }}
          />
          <p style={{ color: "#FFFFFF", marginTop: "10px", fontSize: "18px" }}>
            敬请期待...
          </p>
        </div>
      </div>
    </div>
  );
};

export default PieChart;
