import { combineReducers, configureStore } from "@reduxjs/toolkit";
import navigationReducer from "./slices/navigationSlice";
import userReducer from "./slices/userSlice";

const appReducer = combineReducers({
  navigation: navigationReducer,
  user: userReducer,
});

export const store = configureStore({
  reducer: appReducer,
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
