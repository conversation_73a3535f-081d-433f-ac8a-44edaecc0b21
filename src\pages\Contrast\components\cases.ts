// 案例数据模型定义
export interface CaseFeature {
  value: string;
}

// 移除接口中的 name 字段
export interface CaseEmergencyItem {
  value: string;
}

export interface CaseInsufficientItem {
  value: string;
}

export interface CaseOperationAdjustmentItem {
  value: string;
}

export interface CaseData {
  id: string;
  title: string;
  features: CaseFeature[];
  OperationalImpact: CaseFeature[];
  emergency: CaseEmergencyItem[];
  insufficient: CaseInsufficientItem[];
  insufficientRating: number;
  emergencyRating: number;
  operationAdjustment: CaseOperationAdjustmentItem[];
  adjustmentRating: number;
  overall: string;
  collectivityRating: number;
}

// 创建常量存储 name 值
export const EMERGENCY_NAMES = [
  "对比时分",
  "及时性",
  "规范性",
  "准确性",
  "完整性",
];

export const INSUFFICIENT_NAMES = [
  "ATS操作不规范",
  "信息传递不到位",
  "命令发布不规范",
  "安全把控不到位",
  "协调联动不足",
  "运营调整不合理",
  "时间点把控不规范",
];

export const OPERATION_ADJUSTMENT_NAMES = ["及时性", "合理性"];

// 案例数据 - 只保留 value 字段
const casesData: CaseData[] = [
  {
    id: "case1",
    title: "5号线闵开1303车道岔失事事件",
    features: [{ value: "单一故障" }],
    OperationalImpact: [
      { value: "最大晚点2分钟" },
      { value: "故障持续时长34分钟" },
    ],
    emergency: [
      { value: "无" },
      { value: "--" },
      { value: "--" },
      { value: "--" },
      { value: "--" },
    ],
    insufficient: [
      { value: "1处" },
      { value: "1处" },
      { value: "1处" },
      { value: "1处" },
      { value: "1处" },
      { value: "1处" },
      { value: "1处" },
    ],
    insufficientRating: 4,
    emergencyRating: 5,
    operationAdjustment: [{ value: "--" }, { value: "--" }],
    adjustmentRating: 3,
    overall:
      "本系统测试模拟该的应急处置本系统测试模拟该的应急处置本系统测试模拟该的应急处置...",
    collectivityRating: 4,
  },
  {
    id: "case2",
    title: "5号线闵开1303车道岔失事事件",
    features: [{ value: "单一故障" }],
    OperationalImpact: [
      { value: "最大晚点2分钟" },
      { value: "故障持续时长34分钟" },
    ],
    emergency: [
      { value: "无" },
      { value: "--" },
      { value: "--" },
      { value: "--" },
      { value: "--" },
    ],
    insufficient: [
      { value: "1处" },
      { value: "1处" },
      { value: "1处" },
      { value: "1处" },
      { value: "1处" },
      { value: "1处" },
      { value: "1处" },
    ],
    insufficientRating: 4,
    emergencyRating: 5,
    operationAdjustment: [{ value: "--" }, { value: "--" }],
    adjustmentRating: 3,
    overall:
      "本系统测试模拟该的应急处置本系统测试模拟该的应急处置本系统测试模拟该的应急处置...",
    collectivityRating: 2,
  },
  {
    id: "case3",
    title: "5号线闵开1303车道岔失事事件",
    features: [{ value: "单一故障" }],
    OperationalImpact: [
      { value: "最大晚点2分钟" },
      { value: "故障持续时长34分钟" },
    ],
    emergency: [
      { value: "无" },
      { value: "--" },
      { value: "--" },
      { value: "--" },
      { value: "--" },
    ],
    insufficient: [
      { value: "1处" },
      { value: "1处" },
      { value: "1处" },
      { value: "1处" },
      { value: "1处" },
      { value: "1处" },
      { value: "1处" },
    ],
    insufficientRating: 4,
    emergencyRating: 5,
    operationAdjustment: [{ value: "--" }, { value: "--" }],
    adjustmentRating: 3,
    overall:
      "本系统测试模拟该的应急处置本系统测试模拟该的应急处置本系统测试模拟该的应急处置...",
    collectivityRating: 3,
  },
  {
    id: "case4",
    title: "5号线闵开1303车道岔失事事件",
    features: [{ value: "单一故障" }],
    OperationalImpact: [
      { value: "最大晚点2分钟" },
      { value: "故障持续时长34分钟" },
    ],
    emergency: [
      { value: "无" },
      { value: "--" },
      { value: "较差" },
      { value: "较好" },
      { value: "--" },
    ],
    insufficient: [
      { value: "1处" },
      { value: "1处" },
      { value: "1处" },
      { value: "1处" },
      { value: "1处" },
      { value: "1处" },
      { value: "1处" },
    ],
    insufficientRating: 4,
    emergencyRating: 5,
    operationAdjustment: [{ value: "--" }, { value: "--" }],
    adjustmentRating: 3,
    overall:
      "本系统测试模拟该的应急处置本系统测试模拟该的应急处置本系统测试模拟该的应急处置...",
    collectivityRating: 3,
  },
];

export default casesData;
