interface MockEvent {
  id: string;
  line: string;
  locationType: string;
  startStation: string;
  endStation: string;
  customPosition: string;

  reason: string;
  profession: string;
  delay: string;
  time: string;
}

interface EventChange {
  id: string;
  changes: Record<string, { old: any; new: any }>;
  entity: MockEvent;
}

interface EventDiffResult {
  added: MockEvent[];
  removed: MockEvent[];
  changed: EventChange[];
}
