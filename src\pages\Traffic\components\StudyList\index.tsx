import { darkTheme } from "@/hooks/useTheme";
import { CaretDownOutlined, CaretUpOutlined } from "@ant-design/icons";
import { ConfigProvider, Select } from "antd";
import React, { useState } from "react";
import { IMAGES } from "../../images";
import styles from "./index.module.scss";

const StudyList: React.FC = () => {
  const [selectedLine, setSelectedLine] = useState<string>("all");
  const [select1Open, setSelect1Open] = useState<boolean>(false);
  const [selectedRecordId, setSelectedRecordId] = useState<number | null>(null);
  const handleLineSelect = (value: string) => {
    setSelectedLine(value);
  };

  // 模拟数据
  const studyRecords = [
    {
      id: 1,
      title: "某某某案例学习记录",
      status: "学习中",
      line: "八号线",
      attribution: "原因未知",
      department: "东一区",
      deadline: "8月15日-8月20日",
      completedCount: 16,
      buttonText: "立即学习",
      icon: IMAGES.learningIcon,
    },
    {
      id: 2,
      title: "设备故障案例分析",
      status: "未开始",
      line: "二号线",
      attribution: "设备老化",
      department: "维修部",
      deadline: "8月21日-8月25日",
      completedCount: 0,
      buttonText: "预约学习",
      icon: IMAGES.learningIcon,
    },
    {
      id: 3,
      title: "应急预案演练总结",
      status: "已完成",
      line: "五号线",
      attribution: "演练教学",
      department: "安全部",
      deadline: "8月1日-8月10日",
      completedCount: 23,
      buttonText: "查看详情",
      icon: IMAGES.learningIcon,
    },
    {
      id: 4,
      title: "应急预案演练总结",
      status: "已完成",
      line: "五号线",
      attribution: "演练教学",
      department: "安全部",
      deadline: "8月1日-8月10日",
      completedCount: 23,
      buttonText: "待学习",
      icon: IMAGES.learningIcon,
    },
  ];

  return (
    <ConfigProvider theme={darkTheme}>
      <div className={styles.rightPanel}>
        <div className={styles.rightPanelTitle}>
          <div className={styles.rightPanelTitleLeft}>
            <img src={IMAGES.siftIcon} alt="" />
            案例筛选
          </div>
          <div>
            <Select
              defaultValue="all"
              style={{ width: 180, color: "#fff" }}
              onChange={handleLineSelect}
              onOpenChange={(open) => setSelect1Open(open)}
              suffixIcon={
                select1Open ? (
                  <CaretUpOutlined className={styles.selectIcon} />
                ) : (
                  <CaretDownOutlined className={styles.selectIcon} />
                )
              }
            >
              <Select.Option value="all">全部年份</Select.Option>
              <Select.Option value="fullStack">2025年</Select.Option>
              <Select.Option value="eighteenth">2024年</Select.Option>
            </Select>
          </div>
        </div>
        <div className={styles.rightPanelTop}>
          <div className={styles.rightPanelTopLeft}>
            <div className="w-[40%] h-[90%] flex justify-center items-center">
              <img src={IMAGES.learned} alt="" />
            </div>
            <div className="w-[55%] h-[90%]">
              <div>
                <span className={styles.learnedNum}>35</span>
                <span className={styles.learned}>起</span>
              </div>
              <div className={styles.learnedRecord}>你的学习记录</div>
            </div>
          </div>
          <div className={styles.rightPanelTopRight}>
            <div className="w-[40%] h-[90%] flex justify-center items-center">
              <img src={IMAGES.notStudied} alt="" />
            </div>
            <div className="w-[55%] h-[90%]">
              <div>
                <span className={styles.learnedNum}>5</span>
                <span className={styles.learned}>起</span>
              </div>
              <div className={styles.learnedRecord}>待学习</div>
            </div>
          </div>
        </div>
        <div className={styles.rightPanelContent}>
          {studyRecords.map((record) => (
            <div
              key={record.id}
              className={`${styles.rightPanelContentList} ${
                selectedRecordId === record.id ? styles.selected : ""
              }`}
              onClick={() =>
                setSelectedRecordId(
                  record.id === selectedRecordId ? null : record.id
                )
              }
            >
              <div className={styles.ListTitle}>
                <div className="w-[70%] font-600 text-4">{record.title}</div>
                <div className={styles.ListTitleText}>{record.status}</div>
              </div>
              <div className={styles.ListContent}>
                <div className="w-[50%]">
                  <span style={{ color: "rgba(255,255,255,0.7)" }}>
                    <img src={IMAGES.lineIcon} alt="" />
                    案例线路：
                  </span>
                  <span style={{ color: "#fff" }}>{record.line}</span>
                </div>
                <div className="w-[50%]">
                  <span style={{ color: "rgba(255,255,255,0.7)" }}>
                    <img src={IMAGES.attributionIcon} alt="" />
                    事件归因：
                  </span>
                  <span style={{ color: "#fff" }}>{record.attribution}</span>
                </div>
                <div className="w-[50%]">
                  <span style={{ color: "rgba(255,255,255,0.7)" }}>
                    <img src={IMAGES.learnIcon} alt="" />
                    学习部门：
                  </span>
                  <span style={{ color: "#fff" }}>{record.department}</span>
                </div>
                <div className="w-[50%]">
                  <span style={{ color: "rgba(255,255,255,0.7)" }}>
                    <img src={IMAGES.learnTimeIcon} alt="" />
                    学习期限：
                  </span>
                  <span style={{ color: "#fff" }}>{record.deadline}</span>
                </div>
              </div>
              <div className={styles.ListContentButton}>
                <img src={record.icon} alt="" />
                <div>已有{record.completedCount}人完成学习</div>
                <button>{record.buttonText}</button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </ConfigProvider>
  );
};

export default StudyList;
