{"name": "application-library", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode development", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "publish:stage": "gulp stage"}, "dependencies": {"@gsap/react": "^2.1.2", "@reduxjs/toolkit": "^2.8.2", "@turf/turf": "^7.2.0", "ahooks": "^3.9.0", "animate.css": "^4.1.1", "antd": "^5.26.3", "axios": "^1.11.0", "clsx": "^2.1.1", "color": "^5.0.0", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "echarts": "^5.4.3", "echarts-for-react": "^3.0.2", "echarts-gl": "^2.0.9", "gsap": "^3.13.0", "leaflet": "^1.9.4", "leaflet.heat": "^0.2.0", "proj4leaflet": "^1.0.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-redux": "^9.2.0", "react-router-dom": "^7.8.0", "redux": "^5.0.1"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/color": "^4.2.0", "@types/crypto-js": "^4.2.2", "@types/echarts": "^4.9.22", "@types/leaflet": "^1.9.20", "@types/leaflet.heat": "^0.2.4", "@types/node": "^24.0.10", "@types/proj4leaflet": "^1.0.11", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "eslint": "^9.29.0", "eslint-plugin-react-dom": "^1.52.2", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "eslint-plugin-react-x": "^1.52.2", "globals": "^16.2.0", "gulp": "^5.0.1", "gulp-shell": "^0.8.0", "gulp-zip": "^6.1.0", "postcss": "^8.5.6", "sass": "^1.89.2", "typescript": "~5.8.3", "typescript-eslint": "^8.34.1", "unocss": "^66.3.2", "vite": "^7.0.0", "vite-plugin-svgr": "^4.3.0", "zustand": "^5.0.6"}}