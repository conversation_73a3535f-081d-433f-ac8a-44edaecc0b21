import Color from "color";
import ALL_METRO_LINES_DATA_2025 from "./metro.2025.data";
import { IRawLinePoint, rawDepots } from "./metro.data";

export type MakerDirection = "left" | "right" | "up" | "down";
export interface ILinePoint {
  coord: number[];
  type: "station" | "extension" | "depot";
  name?: string;
  makerDirection: MakerDirection;
}

export const makerDirectionMap: Record<string, MakerDirection> = {
  左: "left",
  右: "right",
  上: "up",
  下: "down",
};

export interface ILineData {
  name: string;
  color: string;
  colorDto: InstanceType<typeof Color>;
  points: ILinePoint[];
  branches: Map<string, ILinePoint[]>;
}

export const lineColorMap2025 = new Map([
  ["1号线", "#e3002b"],
  ["2号线", "#8cc220"],
  ["3号线", "#fcd600"],
  ["4号线", "#461d84"],
  ["5号线", "#944d9a"],
  ["6号线", "#d40068"],
  ["7号线", "#ed6f00"],
  ["8号线", "#0094d8"],
  ["9号线", "#87caed"],
  ["10号线", "#c6afd4"],
  ["11号线", "#871c2b"],
  ["12号线", "#007a60"],
  ["13号线", "#e999c0"],
  ["14号线", "#616020"],
  ["15号线", "#b6a27a"],
  ["16号线", "#98d1c0"],
  ["17号线", "#bc796f"],
  ["18号线", "#c4984e"],
  ["浦江线", "#b5b5b6"],
  ["磁浮线", "#cf5617"],
  ["机场联络线", "#33688A"],
]);
export const getLinePrimaryColor = (line: string) => {
  return lineColorMap2025.get(line) || "#ffffff";
};
export const lineColorArray2025 = Array.from(lineColorMap2025);

// export const lineColorMap2025 = new Map(
//   Array.from(lineMap).map(([name, color]) => {
//     return [name, color];
//   })
// );
// lineColorMap2025.set("机场联络线", "#33688A"); // 市域线

const lineMarkPrefix = "/assets/images/dispatch-center/line-mark/";
const lineMarkSuffix = ".png";
const lineMarkMap: Record<string, string> = {
  "1号线": "1",
  "2号线": "2",
  "3号线": "3",
  "4号线": "4",
  "5号线": "5",
  "6号线": "6",
  "7号线": "7",
  "8号线": "8",
  "9号线": "9",
  "10号线": "10",
  "11号线": "11",
  "12号线": "12",
  "13号线": "13",
  "14号线": "14",
  "15号线": "15",
  "16号线": "16",
  "17号线": "17",
  "18号线": "18",
  "19号线": "19",
  "20号线": "20",
  "21号线": "21",
  "22号线": "22",
  "23号线": "23",
  浦江线: "pujiang",
  磁浮线: "maglev",
  机场联络线: "airport",
};

export function getLineMark(line: string) {
  return lineMarkPrefix + lineMarkMap[line] + lineMarkSuffix;
}

const offset = [0.00185, -0.0045];
// const offset = [0, 0];
export function formatRowPoint(rowLineData: IRawLinePoint): ILinePoint {
  const { 坐标, 类型, 站名, 事件朝向 } = rowLineData;
  const coord = 坐标.split(",").map((e, i) => parseFloat(e) + offset[i]);
  return {
    coord,
    type:
      类型 === "站点" || 类型 === "车站" || 类型.indexOf("站点") !== -1
        ? "station"
        : "extension",
    name: 站名,
    makerDirection: makerDirectionMap[事件朝向 || ""] || "left",
  };
}

export function getLineData(
  lineMap: Map<string, string>,
  lineRawData: Record<string, IRawLinePoint[]>
): ILineData[] {
  return Array.from(lineMap).map(([name, color]) => {
    const data = lineRawData[name];
    const result = {
      name,
      color,
      colorDto: Color(color),
      points: [],
      branches: new Map(),
    } as ILineData;
    if (!data) {
      return result;
    }
    const fork = data.findIndex((p) => p.类型 === "支线站点");
    if (fork === -1) {
      // 没有支线
      result.points = data.map(formatRowPoint);
      return result;
    } else {
      // 有支线
      const branchRegExp = /支线\d+/;
      const branches = new Map();
      data.forEach((p) => {
        const match = p.类型.match(branchRegExp);
        if (match?.length) {
          const branchName = match[0];
          const points = branches.get(branchName) || [];
          points.push(formatRowPoint(p));
          branches.set(branchName, points);
        } else {
          result.points.push(formatRowPoint(p));
        }
      });
      result.branches = branches;
    }

    return result;
  });
}

export const linesData2025: ILineData[] = getLineData(
  lineColorMap2025,
  ALL_METRO_LINES_DATA_2025
);

export const depots = rawDepots.map((d) => {
  const [lat, lon] = d.坐标.split(",").map((s) => parseFloat(s));
  return {
    name: d.基地名称,
    coord: [lat, lon] as [number, number],
    lat: lat,
    lon: lon,
    line: d.线路,
  };
});

export const getLineNameAlias = (lineName: string) =>
  lineName === "机场联络线" ? "机场线" : lineName;
