import badIcon from "@/assets/images/contrast/badIcon.png";
import goodIcon from "@/assets/images/contrast/goodIcon.png";
import { IMAGES } from "@/pages/Traffic/images";
import React from "react";
import casesData, {
  EMERGENCY_NAMES,
  INSUFFICIENT_NAMES,
  OPERATION_ADJUSTMENT_NAMES,
} from "./cases";
import styles from "./index.module.scss";

// 星级评分组件
const StarRating: React.FC<{ stars: number }> = ({ stars }) => {
  return (
    <div className={styles.starRating}>
      {[...Array(5)].map((_, i) => (
        <span key={i}>
          {i < stars ? (
            <img src={IMAGES.star} alt="star" />
          ) : (
            <img src={IMAGES.notStar} alt="" />
          )}
        </span>
      ))}
      <span className={styles.ratingText}>{stars}分</span>
    </div>
  );
};

// 特征列表组件
const FeatureList: React.FC<{ features: { value: string }[] }> = ({
  features,
}) => (
  <div className={styles.featureList}>
    {features.map((feature, index) => (
      <div key={index} className={styles.featureItem}>
        {feature.value}
      </div>
    ))}
  </div>
);

const CaseDetail: React.FC = () => {
  return (
    <div className={styles.caseDetail}>
      <div className={styles.title}>
        <img src={IMAGES.siftIcon} alt="" />
        案例详情
      </div>
      <div className={styles.content}>
        <div className={styles.caseItem}>
          <div className={styles.headerSection}>
            <div className={styles.sectionTitle}>案例名称</div>
            <div className="flex">
              {casesData.map((caseData) => (
                <div key={caseData.id} className={styles.caseHeader}>
                  <div className={styles.caseImagePlaceholder}>
                    <img src={IMAGES.cart} alt="" />
                  </div>
                  <div className={styles.caseTitle}>{caseData.title}</div>
                </div>
              ))}
            </div>
          </div>

          <div className={styles.featureSection}>
            <div className={styles.sectionTitle}>事件特征</div>
            <div className="flex">
              {casesData.map((caseData) => (
                <FeatureList key={caseData.id} features={caseData.features} />
              ))}
            </div>
          </div>

          <div className={styles.impactSection}>
            <div className={styles.sectionTitle}>运营影响</div>
            <div className="flex">
              {casesData.map((caseData) => (
                <FeatureList
                  key={caseData.id}
                  features={caseData.OperationalImpact}
                />
              ))}
            </div>
          </div>

          <div className={styles.sectionRow}>
            <div className={styles.leftColumn}>
              <div className={styles.sectionTitle}>应急处置</div>
              <div className={styles.subItemList}>
                {EMERGENCY_NAMES.map((name, index) => (
                  <div key={index} className={styles.subItem}>
                    {name}
                  </div>
                ))}
              </div>
            </div>
            <div className="flex">
              {casesData.map((caseData) => (
                <div key={caseData.id} className={styles.rightColumn}>
                  {caseData.emergency.map((item, index) => (
                    <div key={index} className={styles.valueItem}>
                      {item.value}
                      {item.value === "较好" && (
                        <div className={styles.icon}>
                          <img src={goodIcon} alt="较好" />
                        </div>
                      )}
                      {item.value === "较差" && (
                        <div className={styles.icon}>
                          <img src={badIcon} alt="较差" />
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              ))}
            </div>
          </div>
          <div className={styles.ratingSection}>
            <div className={styles.sectionTitleFen}>应急处置评分</div>
            <div className="flex">
              {casesData.map((caseData) => (
                <div className={styles.ratingDisplay}>
                  <StarRating stars={caseData.emergencyRating} />
                </div>
              ))}
            </div>
          </div>
          <div className={styles.sectionRow}>
            <div className={styles.leftColumn}>
              <div className={styles.sectionTitle}>处置不足</div>
              <div className={styles.subItemList}>
                {INSUFFICIENT_NAMES.map((name, index) => (
                  <div key={index} className={styles.subItem}>
                    {name}
                  </div>
                ))}
              </div>
            </div>
            <div className="flex">
              {casesData.map((caseData) => (
                <div key={caseData.id} className={styles.rightColumn}>
                  {caseData.insufficient.map((item, index) => (
                    <div key={index} className={styles.valueItem}>
                      {item.value}
                    </div>
                  ))}
                </div>
              ))}
            </div>
          </div>
          <div className={styles.ratingSection}>
            <div className={styles.sectionTitleFen}>处置不足评分</div>
            <div className="flex">
              {casesData.map((caseData) => (
                <div className={styles.ratingDisplay}>
                  <StarRating stars={caseData.insufficientRating} />
                </div>
              ))}
            </div>
          </div>

          <div className={styles.sectionRow}>
            <div className={styles.leftColumn}>
              <div className={styles.sectionTitle}>运营调整</div>
              <div className={styles.subItemList}>
                {OPERATION_ADJUSTMENT_NAMES.map((name, index) => (
                  <div key={index} className={styles.subItem}>
                    {name}
                  </div>
                ))}
              </div>
            </div>
            <div className="flex">
              {casesData.map((caseData) => (
                <div key={caseData.id} className={styles.rightColumn}>
                  {caseData.operationAdjustment.map((item, index) => (
                    <div key={index} className={styles.valueItem}>
                      {item.value}
                    </div>
                  ))}
                </div>
              ))}
            </div>
          </div>

          <div className={styles.ratingSection}>
            <div className={styles.sectionTitleFen}>运营调整评分</div>
            <div className="flex">
              {casesData.map((caseData) => (
                <div className={styles.ratingDisplay}>
                  <StarRating stars={caseData.adjustmentRating} />
                </div>
              ))}
            </div>
          </div>
          <div className={styles.overallSection}>
            <div className={styles.sectionTitle}>整体评分</div>
            <div className="flex">
              {casesData.map((caseData) => (
                <div key={caseData.id} className={styles.overallContent}>
                  <div className={styles.caseTitle}>{caseData.overall}</div>
                </div>
              ))}
            </div>
          </div>

          <div className={styles.ratingSection}>
            <div className={styles.sectionTitleFen}>总体评分</div>
            <div className="flex">
              {casesData.map((caseData) => (
                <div className={styles.ratingDisplay}>
                  <StarRating stars={caseData.collectivityRating} />
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CaseDetail;
