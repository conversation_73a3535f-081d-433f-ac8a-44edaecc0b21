$primary-bg: #1f375b;
$primary-color: #ffffff;
$secondary-color: #90deff;
$accent-color: #19ebff;
$text-shadow: 0px 0px 9px rgba(30, 198, 255, 0.8);
$border-radius: 4px;
$gap: 16px;
$header-height: 90px;

@mixin background-image($url) {
  background-image: url($url);
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}

@mixin title-style($font-size: 24px, $line-height: 40px) {
  font-family: "PingFangSC", "PingFang SC";
  font-weight: 400;
  font-size: $font-size;
  line-height: $line-height;
  font-style: normal;
  background-image: linear-gradient(
    90deg,
    $primary-color 0%,
    $secondary-color 100%
  );
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

@mixin content-container($flex-direction: row) {
  display: flex;
  flex-direction: $flex-direction;
  gap: $gap;
  height: 100%;
}

@mixin scrollbar-style {
  &::-webkit-scrollbar {
    width: 6px;
    background: #1f375b;
  }
  &::-webkit-scrollbar-thumb {
    background: #0c4d87;
    border-radius: 3px;
  }
  &::-webkit-scrollbar-track {
    background: #1f375b;
  }
}

.dashboardContainer {
  width: 100%;
  height: 100%;
  @include background-image("@/assets/images/home/<USER>");

  .dashboardHeader {
    width: 100%;
    height: $header-height;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: transparent;
    @include background-image("@/assets/images/home/<USER>");
    color: $primary-color;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);

    .headerTitle {
      font-family: "PingFangSC", "PingFang" "SC";
      font-weight: 400;
      font-size: 28px;
      color: $primary-color;
      line-height: 32px;
      letter-spacing: 1px;
      text-shadow: 0px 4px 4px #003a80;
      text-align: center;
      font-style: normal;
      text-transform: none;
    }
  }

  .dashboardContent {
    padding: 20px;
    height: calc(100% - $header-height);
    overflow: auto;
  }

  .contentContainer {
    @include content-container;
  }

  .statisticsArea,
  .centralVisualization,
  .sidebar {
    border-radius: $border-radius;
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 23px;
  }

  .statisticsArea {
    flex: 1;
    .chartContainer,
    .pieChart {
      width: 100%;
      flex: 1;
      display: flex;
      flex-direction: column;

      .chartBackground {
        width: 100%;
        min-height: max(calc(100% - 45px), 150px);
        @include background-image("@/assets/images/home/<USER>");
      }
    }

    .pieChart {
      min-height: 180px;
      margin-top: 0;

      .chartBackground {
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .statisticsTitle {
      width: 100%;
      min-height: 120px;
      margin-top: 0;
      @include background-image("@/assets/images/home/<USER>");
      position: relative;

      .mainNumber {
        font-family: "PangMenZhengDao", "PangMenZhengDao";
        font-weight: normal;
        font-size: 68px;
        line-height: 78px;
        font-style: normal;
        background-image: linear-gradient(
          180deg,
          $primary-color 0%,
          $secondary-color 100%
        );
        -webkit-background-clip: text;
        background-clip: text;
        color: transparent;
      }

      .cardLabel {
        font-family: "PingFangSC", "PingFang SC";
        font-weight: 400;
        font-size: 20px;
        color: $primary-color;
        line-height: 33px;
        text-shadow: $text-shadow;
        font-style: normal;
      }
    }
  }

  .centralVisualization {
    flex: 2; /* 占2份宽度 */

    .revolve {
      width: 100%;
      flex: 3;
      min-height: 0;
      position: relative;
      display: flex;
      flex-wrap: wrap;
      justify-content: center;

      .individualBase {
        width: 120px;
        height: 140px;
        @include background-image("@/assets/images/home/<USER>/notSelected.png");
        position: absolute;
      }

      .sift {
        width: 90%;
        height: 15%;
        color: $primary-color;
        display: flex;
        justify-content: right;
      }

      .tipImage {
        width: 28%;
        height: 30%;
        position: absolute;
        top: 10%;
        @include background-image("@/assets/images/home/<USER>/textBox.png");
        color: $primary-color;
        padding: 15px;
        .tipText {
          width: 100%;
          height: 85%;
          overflow-y: auto;
          -ms-overflow-style: none;
          scrollbar-width: none;
          &::-webkit-scrollbar {
            width: 0;
            background: $primary-bg;
          }
        }
      }

      .centerTable {
        width: 100%;
        height: 85%;
        display: flex;
        justify-content: center;
        align-items: center;

        .centerImage {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }

      // 位置类
      .individual1 {
        top: 30%;
        left: 2%;
      }
      .individual2 {
        top: 16%;
        left: 22%;
      }
      .individual3 {
        top: 16%;
        right: 22%;
      }
      .individual4 {
        top: 30%;
        right: 2%;
      }
      .individual5 {
        top: 55%;
        right: 10%;
      }
      .individual6 {
        top: 55%;
        left: 10%;
      }
      .individual7 {
        top: 70%;
        right: 42%;
      }
    }

    .chartContainer {
      width: 100%;
      flex: 2;
      min-height: 0;
      margin-top: 0;

      .herTitle {
        width: 100%;
        height: 50px;
        @include background-image("@/assets/images/home/<USER>");
        display: flex;
        justify-content: space-between;
        align-items: center;

        .title {
          @include title-style(24px, 33px);
          text-align: justify;
        }

        .date {
          font-family: "PingFangSC", "PingFang SC";
          font-weight: 500;
          font-size: 14px;
          color: $accent-color;
          line-height: 14px;
          text-align: right;
          font-style: normal;
          padding-right: 15px;
        }
      }

      .gridContainer {
        width: 100%;
        max-height: calc(100% - 50px);
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
        padding: 10px;
        overflow-y: auto;
        @include scrollbar-style;

        .list {
          width: 32%;
          height: 67px;
          @include background-image("@/assets/images/home/<USER>");
          display: flex;
          justify-content: space-between;
          align-items: center;

          .left {
            width: 30%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;

            img {
              background-size: 100% 100%;
              background-position: center;
              background-repeat: no-repeat;
            }
          }

          .right {
            width: 70%;
            height: 100%;
            color: $primary-color;
            padding-right: 5px;

            .listTitle {
              width: 100%;
              height: 38px;
              font-family: "PingFangSC", "PingFang SC";
              font-weight: 400;
              font-size: 14px;
              color: $primary-color;
              text-shadow: $text-shadow;
              text-align: left;
              font-style: normal;
              padding: 8px 0;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }

            .progressRedText {
              color: $primary-color;
            }
          }
        }
      }
    }
  }

  .sidebar {
    flex: 1; /* 占1份宽度 */
    gap: 10px;

    .queryModule {
      width: 100%;
      flex: 1;
      min-height: 0;
      margin-top: 0;
      position: relative;

      .chartTitle {
        width: 100%;
        height: 45px;
        @include background-image("@/assets/images/home/<USER>");

        span {
          @include title-style;
          margin-left: 37px;
          text-align: justify;
        }
      }

      .queryArea {
        width: 100%;
        min-height: max(calc(100% - 12px));
        @include background-image("@/assets/images/home/<USER>");
        position: absolute;
        top: 0;
        display: flex;
        justify-content: space-around;
        align-items: center;
        padding-top: 60px;

        .queryAreaImg {
          width: 35%;
          min-height: 150px;
          @include background-image("@/assets/images/home/<USER>");
          display: flex;
          justify-content: center;
          align-items: flex-end;

          span {
            color: $primary-color;
          }
        }
      }
    }

    .moduleSelector {
      width: 100%;
      flex: 2;
      min-height: 0;
      margin-top: 0;
      @include background-image("@/assets/images/home/<USER>");
    }
  }

  .chartTitle {
    width: 100%;
    height: 45px;
    @include background-image("@/assets/images/home/<USER>");

    span {
      @include title-style;
      margin-left: 37px;
      text-align: justify;
    }
  }
}
