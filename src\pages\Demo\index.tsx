import React from "react";
import {
  ConfigProvider,
  Button,
  Select,
  Space,
  Pagination,
  PaginationProps,
  DatePicker,
  DatePickerProps,
} from "antd";
import { Link } from "react-router-dom";
import { darkTheme } from "../../hooks/useTheme";

const Home: React.FC = () => {
  return (
    <div
      style={{ backgroundColor: "#040A1E ", width: "100vw", height: "100vh" }}
    >
      <ConfigProvider
        theme={darkTheme}
      >
        <div className="flex flex-col items-center justify-center h-screen">
          <Space wrap>
            <div>
              <Link to="/">
                <Button type="primary">返回首页</Button>
              </Link>
            </div>
            <div>
              <Select
                defaultValue="lucy"
                style={{ width: 120 }}
                options={[
                  { value: "jack", label: "Jack" },
                  { value: "lucy", label: "Lucy" },
                  { value: "Yiminghe", label: "yiminghe" },
                  { value: "disabled", label: "Disabled", disabled: true },
                ]}
              ></Select>
            </div>
            <div>
              <Pagination
                showSizeChanger
                onShowSizeChange={onShowSizeChange}
                defaultCurrent={3}
                total={500}
              />
              <br />
            </div>
          </Space>
          <div>
            <DatePicker onChange={onChange} />
          </div>
        </div>
      </ConfigProvider>
    </div>
  );
};

const onShowSizeChange: PaginationProps["onShowSizeChange"] = (
  current,
  pageSize
) => {
  console.log(current, pageSize);
};

const onChange: DatePickerProps["onChange"] = (date, dateString) => {
  console.log(date, dateString);
};

export default Home;
