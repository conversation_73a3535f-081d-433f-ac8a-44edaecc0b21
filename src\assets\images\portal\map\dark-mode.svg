<svg
  id="svgfilters"
  aria-hidden="true"
  style="position: absolute; width: 0; height: 0; overflow: hidden"
  version="1.1"
  xmlns="http://www.w3.org/2000/svg"
  xmlns:xlink="http://www.w3.org/1999/xlink"
>
  <defs>
    <filter
      id="x-rays"
      x="-10%"
      y="-10%"
      width="120%"
      height="120%"
      filterUnits="objectBoundingBox"
      primitiveUnits="userSpaceOnUse"
      color-interpolation-filters="sRGB"
    >
      <feColorMatrix
        type="matrix"
        values=".13 .23 .73 0 0
					.43 .43 .03 0 0
					.43 .43 .03 0 0
					0 0 0 1 0"
        in="SourceGraphic"
        result="colormatrix"
      />
      <feComponentTransfer in="colormatrix" result="componentTransfer">
        <feFuncR type="table" tableValues="0.33 0.3 0.25" />
        <feFuncG type="table" tableValues="0.33 0.44 0.24" />
        <feFuncB type="table" tableValues="0.91 0.62 0.29" />
        <feFuncA type="table" tableValues="0 1" />
      </feComponentTransfer>
      <feBlend
        mode="normal"
        in="componentTransfer"
        in2="SourceGraphic"
        result="blend"
      />
    </filter>
  </defs>
</svg>
