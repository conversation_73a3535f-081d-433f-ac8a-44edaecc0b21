import request from "@/utils/request";

const USER_PREFIX = "/api/";

export const UserApi = {
  login(data: User.LoginParams) {
    return request.post<null, User.ISimpleUser>(
      `${USER_PREFIX}login/index`,
      data
    );
  },

  getInfoByUserId(userId: number) {
    return request.get<null, User.IUserDetailResponse>(
      `${USER_PREFIX}user/info`,
      {
        params: { userId },
      }
    );
  },
};
