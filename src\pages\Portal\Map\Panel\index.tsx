import { CaretDownOutlined, CaretUpOutlined } from "@ant-design/icons";
import { useBoolean } from "ahooks";
import { Select } from "antd";
import clsx from "clsx";
import { useState } from "react";
import { FilterState } from "../useMapState";
import NumberStatistics from "./NumberStatistics";
import styles from "./portal.panel.module.scss";
import ReasonStatistics from "./ReasonStatistics";

interface Props {
  values: FilterState;
  onChange: (values: FilterState) => void;
  events: MockEvent[];
}

const Title = ({ text }: { text: string }) => {
  return (
    <div className={styles.title}>
      <span className={clsx(styles.triangle, "mr-1")}></span>
      <span className={clsx(styles.blueFont, styles.titleFont)}>{text}</span>
    </div>
  );
};

const PortalPanel = (p: Props) => {
  const [open, { set: setOpen }] = useBoolean(false);
  const [annual, setAnnual] = useState(2025);
  const onAnnualChange = (v: number) => {
    setAnnual(v);
  };

  return (
    <div className={styles.portalPanel}>
      <div className={styles.header}>
        <div>
          <span className={clsx(styles.blueFont, styles.headerFont)}>
            事件归因
          </span>
        </div>
        <div>
          <span className="mr-2">年份选择</span>
          <Select
            className={styles.annualSelect}
            defaultValue={annual}
            onChange={(v) => onAnnualChange(v)}
            onOpenChange={setOpen}
            suffixIcon={
              open ? (
                <CaretUpOutlined className={styles.selectIcon} />
              ) : (
                <CaretDownOutlined className={styles.selectIcon} />
              )
            }
          >
            <Select.Option value="all">全部年份</Select.Option>
            <Select.Option value={2025}>2025年度</Select.Option>
          </Select>
        </div>
      </div>
      <Title text="归因数量统计" />
      <NumberStatistics events={p.events} />
      <Title text="事件原因" />
      <ReasonStatistics events={p.events} />
    </div>
  );
};

export default PortalPanel;
