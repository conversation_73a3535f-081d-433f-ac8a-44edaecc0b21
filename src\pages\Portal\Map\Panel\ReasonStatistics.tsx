import dispatchIcon from "@/assets/images/portal/map/panel/dispatch-icon.png";
import electricIcon from "@/assets/images/portal/map/panel/electric-icon.png";
import objectivesIcon from "@/assets/images/portal/map/panel/objectives-icon.png";
import trainIcon from "@/assets/images/portal/map/panel/train-icon.png";
import transportIcon from "@/assets/images/portal/map/panel/transport-icon.png";
import wifiIcon from "@/assets/images/portal/map/panel/wifi-icon.png";
import workIcon from "@/assets/images/portal/map/panel/work-icon.png";
import { useUpdateEffect } from "ahooks";
import clsx from "clsx";
import { CSSProperties, useState } from "react";
import styles from "./panel.reason.module.scss";
import panelStyles from "./portal.panel.module.scss";

const reasonIconMap: Record<string, string> = {
  车辆: trainIcon,
  通号: wifiIcon,
  供电: electricIcon,
  工务: workIcon,
  调度: dispatchIcon,
  客运: transportIcon,
  客观: objectivesIcon,
};
const getReasonIcon = (reason: string) => {
  return reasonIconMap[reason] || trainIcon;
};

interface CategoryItem {
  name: string;
  value: number;
  types: { name: string; value: number; percentage: number }[];
}

const meta = [
  {
    name: "车辆",
    types: [
      "控制系统故障",
      "车门故障",
      "制动故障",
      "牵引故障",
      "辅控系统故障",
      "车辆其他",
    ],
  },
  {
    name: "供电",
    types: ["变电站故障", "接触网/轨故障	", "供电其他"],
  },
  {
    name: "调度",
    types: ["调度员", "日常工作要求	", "调度其他"],
  },
  {
    name: "客运",
    types: [
      "车站值班员",
      "司机",
      "屏蔽门/安全门",
      "故障",
      "运转及信号楼",
      "客运其他原因",
    ],
  },
  {
    name: "通号",
    types: [
      "车载ATC故障",
      "轨旁设备故障",
      "ATS故障",
      "道岔故障",
      "信号",
      "通讯失效",
      "通号其他",
    ],
  },
  {
    name: "工务",
    types: ["线路施工", "道岔故障", "工务结构损坏", "工务其他"],
  },
  {
    name: "客观",
    types: [
      "大客流",
      "天气",
      "异物侵限",
      "人员侵限",
      "乘客触发 ",
      "门夹人夹物",
      "公共安全",
      "客观其他",
    ],
  },
];

const getCategoryItemData = (c: string, events: MockEvent[]) => {
  const v = meta.find((v) => v.name === c)!;
  const total = events.filter((e) => e.reason === c).length;
  const types = v.types.map((t) => {
    const value = events.filter(
      (e) => e.reason === c && e.profession === t
    ).length;
    return {
      name: t,
      value: value,
      percentage: 0,
    };
  });
  const max = Math.max(...types.map((t) => t.value));
  types.forEach((t) => {
    t.percentage = Math.round((t.value / max) * 100);
  });
  types.sort((a, b) => b.value - a.value);
  return {
    name: c,
    value: total,
    types: types,
  };
};

const getLeftData = (evs: MockEvent[]): CategoryItem[] => {
  const lettCategories = meta.slice(0, 4);
  return lettCategories.map((v) => {
    return getCategoryItemData(v.name, evs);
  });
};

const getRightData = (evs: MockEvent[]): CategoryItem[] => {
  const rightCategories = meta.slice(4);
  return rightCategories.map((v) => {
    return getCategoryItemData(v.name, evs);
  });
};

const initLeftData = getLeftData([]);
const initRightData = getRightData([]);

interface TypeItem {
  category: string;
  name: string;
  value: number;
  percentage: number;
  index: number;
}
const ReasonTypeItem = (d: TypeItem) => {
  return (
    <div
      className={clsx(styles.typeItem, "px-2")}
      style={{
        marginBottom: d.category === "客观" ? 6 : 0,
      }}
      key={d.name}
    >
      <div className="flex" style={{ width: 100 }}>
        <span
          className={clsx(styles.rankFont, {
            [styles.first]: d.index === 0,
            [styles.second]: d.index === 1,
            [styles.third]: d.index === 2,
          })}
        >
          {d.index + 1}{" "}
        </span>
        <span className="flex-1">{d.name}</span>
      </div>
      <div className="flex-1 flex-center">
        <div
          className="w-full pl-1"
          style={{ "--percentage": d.percentage } as CSSProperties}
        >
          <div className={styles.percentage}></div>
        </div>
      </div>
      <div style={{ width: 47, textAlign: "right" }}>
        <span className={styles.valueFont}>{d.value}</span>
        <span className={styles.unitFont}>起</span>
      </div>
    </div>
  );
};

const ReasonCategoryItem = ({ d }: { d: CategoryItem }) => {
  return (
    <div className={styles.box}>
      <div className={styles.header}>
        <div className="flex-center">
          <img src={getReasonIcon(d.name)} alt="" />
          <span className={styles.headerFont} style={{ fontSize: 16 }}>
            {d.name}
          </span>
        </div>
        <div>
          <span
            className={clsx(panelStyles.blueFont)}
            style={{ fontSize: 18, fontFamily: "AlimamaShuHeiTi" }}
          >
            {d.value}
          </span>
          <span
            style={{
              fontSize: 12,
              fontFamily: "AlimamaShuHeiTi",
              color: "rgba(234,249,255,0.8)",
            }}
          >
            起
          </span>
        </div>
      </div>
      <div
        className={clsx(styles.content)}
        style={{ padding: "8px 0", paddingBottom: d.name === "客观" ? 0 : 8 }}
      >
        {d.types.map((s, i) => {
          return (
            <ReasonTypeItem category={d.name} {...s} key={s.name} index={i} />
          );
        })}
      </div>
    </div>
  );
};

interface Props {
  events: MockEvent[];
}

function ReasonStatistics({ events }: Props) {
  const [leftData, setLeftData] = useState(initLeftData);
  const [rightData, setRightData] = useState(initRightData);

  useUpdateEffect(() => {
    setLeftData(getLeftData(events));
    setRightData(getRightData(events));
  }, [events]);

  return (
    <div
      className="flex overflow-y-auto custom-scroll-bar"
      style={{ height: "calc(100% - 375px)" }}
    >
      <div className="flex pl-5">
        <div className="mr-2">
          {leftData.map((d) => {
            return <ReasonCategoryItem d={d} key={d.name} />;
          })}
        </div>
        <div>
          {rightData.map((d) => {
            return <ReasonCategoryItem d={d} key={d.name} />;
          })}
        </div>
      </div>
    </div>
  );
}

export default ReasonStatistics;
