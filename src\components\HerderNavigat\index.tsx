import contrast from "@/assets/images/traffic/contrast.png";
import regulations from "@/assets/images/traffic/regulations.png";
import wisdom from "@/assets/images/traffic/wisdom.png";
import { RootState } from "@/store";
import { setActiveButton, setActiveTab } from "@/store/slices/navigationSlice";
import { CaretDownOutlined, HomeFilled } from "@ant-design/icons";
import { Button, Dropdown, MenuProps, Space } from "antd";
import { Header } from "antd/es/layout/layout";
import React from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import styles from "./index.module.scss";

const Navigation: React.FC = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const user = useSelector((state: RootState) => state.user.currentUser);

  const handleButtonClick = (buttonName: string) => {
    dispatch(setActiveButton(buttonName));
    if (buttonName === "进入后台") {
      window.open("/#/personal/data");
    }
  };
  const handleTabClick = (tabName: string) => {
    dispatch(setActiveTab(tabName));
  };

  const isPortalRoute = location.pathname.includes("/portal");
  const menus: MenuProps["items"] = [
    {
      label: (
        <a
          href="https://www.antgroup.com"
          target="_blank"
          rel="noopener noreferrer"
        >
          1st menu item
        </a>
      ),
      key: "0",
    },
    {
      label: (
        <a
          href="https://www.aliyun.com"
          target="_blank"
          rel="noopener noreferrer"
        >
          2nd menu item
        </a>
      ),
      key: "1",
    },
  ];
  return (
    <Header className={styles.header}>
      <div className={styles.title}>上海轨道交通案例智汇平台</div>
      <div className={styles.navGroup}>
        <Button
          className={styles.navButton}
          onClick={() => {
            handleButtonClick("首页");
            navigate("/portal");
          }}
        >
          首页
        </Button>
        <Button
          className={styles.navButton}
          onClick={() => {
            handleButtonClick("上海地铁案例");
            navigate("/traffic");
          }}
        >
          上海地铁案例
        </Button>
        <Button
          className={styles.navButton}
          onClick={() => handleButtonClick("国内外案例")}
        >
          国内外案例
        </Button>
      </div>
      <div className={styles.tapGroup}>
        {!isPortalRoute && (
          <Button
            className={styles.tapButton}
            onClick={() => {
              handleTabClick("案例对比");
              navigate("/resources/contrast");
            }}
          >
            <img src={contrast} alt="" />
            案例对比
          </Button>
        )}
        <Button
          className={styles.tapButton}
          onClick={() => {
            handleTabClick("规章库");
            navigate("/resources/regulations");
          }}
        >
          <img src={regulations} alt="" />
          规章库
        </Button>
        <Button
          className={styles.tapButton}
          onClick={() => {
            handleTabClick("智汇看板");
            navigate("/dashboardScreen");
          }}
        >
          <img src={wisdom} alt="" />
          智汇看板
        </Button>
      </div>
      <div className={styles.empty}>
        <Dropdown menu={{ items: menus }} trigger={["click"]}>
          <a
            className="text-white hover:text-blue-500 pointer-events-auto cursor-pointer"
            onClick={(e) => e.preventDefault()}
          >
            <Space>
              <span>{user?.username}</span>
              <span>{user?.employment}</span>
              <CaretDownOutlined />
            </Space>
          </a>
        </Dropdown>
        <Button
          className={styles.adminButton}
          icon={<HomeFilled />}
          onClick={() => handleButtonClick("进入后台")}
        >
          进入后台
        </Button>
      </div>
    </Header>
  );
};

export default Navigation;
