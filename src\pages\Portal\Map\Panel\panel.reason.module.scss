.box {
  width: 276px;
  margin-bottom: 9px;
  .header {
    height: 32px;
    line-height: 32px;
    background-image: url("@/assets/images/portal/map/panel/box-header-bg.svg");
    background-size: 100% 100%;
    background-position: left;
    background-repeat: no-repeat;
    color: #eaf9ff;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 12px 0 4px;
  }
  .content {
    background: radial-gradient(
      0% 115% at -15% 100%,
      rgba(60, 126, 255, 0.08) 0%,
      rgba(60, 126, 255, 0) 100%
    );
    box-shadow: inset 0px 0px 8px 0px rgba(2, 93, 244, 0.1);
    border-radius: 4px 4px 4px 4px;
    border: 1px solid;
    border-image: linear-gradient(
        346deg,
        rgba(41, 202, 255, 0.5),
        rgba(41, 202, 255, 0)
      )
      1 1;
    border-radius: 10px;
    color: rgba(234, 249, 255, 0.8);
  }
}

@mixin colorFont($c1, $c2) {
  background-image: linear-gradient(180deg, $c1 46%, $c2 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.typeItem {
  line-height: 20px;
  display: flex;
  height: 20px;
  .rankFont {
    font-family: "AlimamaShuHeiTi";
    font-weight: 700;
    margin-right: 4px;
    font-size: 12px;
    color: #ffffff;

    &.first {
      @include colorFont(#fcca27, #eb8a14);
    }
    &.second {
      @include colorFont(#dee6f4, #5c7db8);
    }
    &.third {
      @include colorFont(#f79f7c, #ac4c26);
    }
  }
  .valueFont {
    font-family: "AlimamaShuHeiTi";
    color: #29caff;
    font-size: 16px;
  }
  .unitFont {
    font-family: "AlimamaShuHeiTi";
    margin-left: 2px;
  }
}

.percentage {
  width: 100%;
  height: 2px;
  background: #294072;
  border-radius: 2px 2px 2px 2px;
  position: relative;

  &::after {
    content: "";
    display: block;
    width: var(--percentage);
    height: 100%;
    background: linear-gradient(90deg, #b2ecff 0%, #29caff 100%);
    border-radius: 2px 2px 2px 2px;
    transition: width 0.3s ease;
    position: absolute;
    left: 0;
    top: 0;
  }
}
