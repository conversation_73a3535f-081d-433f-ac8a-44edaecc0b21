import { <PERSON><PERSON><PERSON> } from "@/apis/case";
import { useRequest } from "ahooks";
import { useCallback, useMemo } from "react";

function getDicIdMapping(acc: Record<number, string>, item: IDirectory) {
  acc[item.id] = item.name;
  if (item.children && item.children.length > 0) {
    item.children.forEach((element) => {
      getDicIdMapping(acc, element);
    });
  }
  return acc;
}
function getDicNameMapping(acc: Record<string, number>, item: IDirectory) {
  acc[item.name] = item.id;
  if (item.children && item.children.length > 0) {
    item.children.forEach((element) => {
      getDicNameMapping(acc, element);
    });
  }
  return acc;
}

function useDict() {
  const { data } = useRequest(CaseApi.getDictionary, {
    // manual: false,
  });
  const dictList = useMemo(() => {
    return data?.data || [];
  }, [data]);

  const dicMapping = useMemo(() => {
    return dictList.reduce((acc, item) => {
      return getDicIdMapping(acc, item);
    }, {} as Record<number, string>);
  }, [dictList]);

  const dicNameMapping = useMemo(() => {
    return dictList.reduce((acc, item) => {
      return getDicNameMapping(acc, item);
    }, {} as Record<string, number>);
  }, [dictList]);

  const dicId = useCallback(
    (id: number) => {
      return dicMapping[id];
    },
    [dicMapping]
  );

  const dicName = useCallback(
    (name: string) => {
      return dicNameMapping[name];
    },
    [dicNameMapping]
  );

  const getOptionsById = useCallback(
    (id: number): OptionWith<number>[] => {
      const dict = dictList.filter((d) => d.pid === id);
      return dict.map((d) => ({ value: d.id, label: d.name }));
    },
    [dictList]
  );
  /** 重名会出错 */
  const getOptionsByName = useCallback(
    (name: string): OptionWith<number>[] => {
      const dict = dictList.find((d) => d.name === name);
      if (!dict) return [];
      return getOptionsById(dict.id);
    },
    [dictList]
  );

  const reasonCategories = useMemo(() => {
    return getOptionsByName("归因").map((d) => ({
      ...d,
      types: getOptionsById(d.value),
    }));
  }, [getOptionsByName]);

  return {
    dictList,
    /** id => name */
    dicId,
    /** name => id */
    dicName,
    /** pid => options */
    getOptionsById,
    /** 重名会出错 */
    getOptionsByName,
    reasonCategories,
  };
}
export default useDict;
