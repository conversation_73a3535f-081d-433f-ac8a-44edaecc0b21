import { store } from "@/store";
import { fetchUserById } from "@/store/slices/userSlice";
import { toLogin } from "@/utils/assistant";
import customStorage from "@/utils/storage";
import { useMount } from "ahooks";

function Auth() {
  useMount(async () => {
    const token = customStorage.get!("token");
    const userId = customStorage.get!("userId");
    if (!token || !userId) {
      toLogin();
      return;
    }
    store.dispatch(fetchUserById(Number(userId))).then((res) => {
      if (res.payload && res.payload.msg) {
        if (res.payload.msg.includes("用户身份失效")) {
          customStorage.remove("token");
          toLogin();
          return Promise.reject(res);
        }
      }
    });
  });
  return <></>;
}

export default Auth;
