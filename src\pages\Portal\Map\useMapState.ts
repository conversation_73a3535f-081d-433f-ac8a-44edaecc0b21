import { lineColorArray2025 } from "@/utils/map/meta";
import { useMount, useRequest, useUpdateEffect } from "ahooks";
import { useState } from "react";
import { dummyEvents } from "./mock";

import dateTypeIcon from "@/assets/images/portal/map/filter/date-type-icon.png";
import delayIcon from "@/assets/images/portal/map/filter/delay-icon.png";
import durationTypeIcon from "@/assets/images/portal/map/filter/duration-type-icon.png";
import reasonIcon from "@/assets/images/portal/map/filter/reason-icon.png";
import useDict from "@/hooks/useDict";

export interface FilterState {
  reason: string;
  delay: string;
  dateType: string;
  durationType: string;
  annual: string;
  lines: string[];
}

const mockCasesApi = async (f: FilterState) => {
  let res = dummyEvents;
  if (f.reason !== "全部") {
    res = res.filter((ev) => ev.reason === f.reason);
  }
  res = res.filter((ev) => f.lines.includes(ev.line));
  return res;
};

const states: FilterState = {
  reason: "全部",
  delay: "全部",
  dateType: "全部",
  durationType: "全部",
  annual: "全部",
  lines: lineColorArray2025.map((l) => l[0]),
};

export const _categoryOptions = [
  {
    key: "reason",
    label: "事件归因",
    originLabel: "事件归因",
    icon: reasonIcon,
    children: [
      { value: "全部", label: "全部" },
      { value: "车辆", label: "车辆" },
      { value: "通号", label: "通号" },
      { value: "供电", label: "供电" },
      { value: "工务", label: "工务" },
      { value: "调度", label: "调度" },
      { value: "客运", label: "客运" },
      { value: "客观", label: "客观" },
    ],
  },
  {
    key: "delay",
    label: "晚点时间",
    originLabel: "晚点时间",
    icon: delayIcon,
    children: [
      { value: "全部", label: "全部" },
      { value: "2", label: "2分钟以上" },
      { value: "5", label: "5分钟以上" },
      { value: "15", label: "15分钟以上" },
      { value: "30", label: "30分钟以上" },
      { value: "other", label: "其他" },
    ],
  },
  {
    key: "dateType",
    label: "日期类型",
    originLabel: "日期类型",
    icon: dateTypeIcon,
    children: [
      { value: "全部", label: "全部" },
      { value: "workday", label: "工作日" },
      { value: "weekend", label: "双休日" },
      { value: "holiday", label: "节假日" },
    ],
  },
  {
    key: "durationType",
    label: "时段类型",
    originLabel: "时段类型",
    icon: durationTypeIcon,
    children: [
      { value: "全部", label: "全部" },
      { value: "peak", label: "高峰" },
      { value: "offPeak", label: "平峰" },
      { value: "low", label: "低谷" },
    ],
  },
];

export default function useMapState() {
  const { reasonCategories } = useDict();
  const { data: cases, run: fetchCases } = useRequest(mockCasesApi, {
    manual: true,
  });

  const [filterValues, setFilterValues] = useState<FilterState>(states);
  const [categoryOptions, setCategoryOptions] = useState(_categoryOptions);

  useUpdateEffect(() => {
    setCategoryOptions((prev) => {
      return prev.map((category) => {
        if (category.key === "reason") {
          return {
            ...category,
            children: reasonCategories.map((d) => ({
              value: d.label, // maybe should be d.key
              label: d.label,
            })),
          };
        }
        return category;
      });
    });
  }, [reasonCategories]);

  function updateCategoryLabel(categoryKey: string, key: string | number) {
    const newCategoryOptions = categoryOptions.map((category) => {
      if (category.key === categoryKey) {
        return {
          ...category,
          label:
            key === "全部"
              ? category.originLabel
              : category.children.find((op) => op.value === key)!.label,
        };
      }
      return category;
    });
    setCategoryOptions(newCategoryOptions);
  }

  useMount(() => {
    fetchCases(filterValues);
  });
  useUpdateEffect(() => {
    fetchCases(filterValues);
  }, [filterValues, fetchCases]);

  return {
    cases,
    filterValues,
    setFilterValues,
    categoryOptions,
    updateCategoryLabel,
  };
}
