import { CSSProperties } from "react";

interface DotSpinnerProps {
  size?: number;
  color?: string;
  speed?: number;
  dotCount?: number;
}

export default function DotSpinner({
  size = 48,
  color = "#3b82f6",
  speed = 1.2,
  dotCount = 8,
}: DotSpinnerProps) {
  const containerStyle: CSSProperties = {
    position: "relative",
    width: size,
    height: size,
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    minHeight: "100vh",
    minWidth: "100vw",
    backgroundColor: "transparent",
    margin: "0 auto",
  };

  const dotBaseStyle: CSSProperties = {
    position: "absolute",
    width: size * 0.15,
    height: size * 0.15,
    borderRadius: "50%",
    backgroundColor: color,
    transformOrigin: `${size / 2}px ${size / 2}px`,
    animation: `spin ${speed}s linear infinite`, // 修改动画为线性旋转
  };

  return (
    <div style={containerStyle}>
      {Array.from({ length: dotCount }).map((_, index) => (
        <div
          key={index}
          style={{
            ...dotBaseStyle,
            animationDelay: `${index * (-speed / dotCount)}s`,
            transform: `rotate(${index * (360 / dotCount)}deg) translate(${
              size * 0.35
            }px)`,
          }}
        />
      ))}

      <style>{`
          @keyframes spin {
            from {
              transform: rotate(0deg);
            }
            to {
              transform: rotate(360deg);
            }
          }
        `}</style>
    </div>
  );
}
