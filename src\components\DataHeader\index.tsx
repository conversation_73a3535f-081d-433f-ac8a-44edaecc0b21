import homeGo from "@/assets/images/contrast/homeGo.png";
import homeIcon from "@/assets/images/contrast/homeicon.png";
import contrast from "@/assets/images/traffic/contrast.png";
import regulations from "@/assets/images/traffic/regulations.png";
import wisdom from "@/assets/images/traffic/wisdom.png";
import { darkTheme } from "@/hooks/useTheme";
import { RootState } from "@/store";
import { setActiveTab } from "@/store/slices/navigationSlice";
import { CaretDownOutlined, HomeFilled } from "@ant-design/icons";
import { Button, ConfigProvider, Dropdown, MenuProps, Space } from "antd";
import { Header } from "antd/es/layout/layout";
import React from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import styles from "./index.module.scss";

const Navigation: React.FC = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const isHistoryMode = useSelector(
    (state: RootState) => state.navigation.activeTab
  );
  const handleTabClick = (tabName: string) => {
    dispatch(setActiveTab(tabName));
    if (tabName === "进入后台") {
      window.open("/#/personal/data");
    }
  };
  const menus: MenuProps["items"] = [
    {
      label: (
        <a
          href="https://www.antgroup.com"
          target="_blank"
          rel="noopener noreferrer"
        >
          1st menu item
        </a>
      ),
      key: "0",
    },
    {
      label: (
        <a
          href="https://www.aliyun.com"
          target="_blank"
          rel="noopener noreferrer"
        >
          2nd menu item
        </a>
      ),
      key: "1",
    },
  ];
  return (
    <ConfigProvider theme={darkTheme}>
      <Header className={styles.header}>
        <div className={styles.title}>上海轨道交通案例智汇平台</div>
        <div className={styles.tapGroup}>
          <Button
            className={`${styles.tapButton} ${
              isHistoryMode === "案例对比" ? styles.activeTap : ""
            }`}
            onClick={() => {
              handleTabClick("案例对比");
              navigate("/resources/contrast");
            }}
          >
            <img src={regulations} alt="" />
            案例对比
          </Button>
          <Button
            className={`${styles.tapButton} ${
              isHistoryMode === "规章库" ? styles.activeTap : ""
            }`}
            onClick={() => {
              handleTabClick("规章库");
              navigate("/resources/regulations");
            }}
          >
            <img src={contrast} alt="" />
            规章库
          </Button>
          <Button
            className={styles.tapButton}
            onClick={() => {
              handleTabClick("智汇看板");
              navigate("/dashboardScreen");
            }}
          >
            <img src={wisdom} alt="" />
            智汇看板
          </Button>
        </div>
        <div className={styles.empty}>
          <Button
            className={styles.adminButton}
            onClick={() => {
              handleTabClick("进入后台");
              window.open("/#/personal/data");
            }}
          >
            <img src={homeIcon} alt="" />
            首页
            <img src={homeGo} alt="" style={{ marginLeft: 10 }} />
          </Button>
          <Dropdown menu={{ items: menus }} trigger={["click"]}>
            <a
              className="text-white hover:text-blue-500 pointer-events-auto cursor-pointer mr-4"
              onClick={(e) => e.preventDefault()}
            >
              <Space>
                <span>用户名</span>
                <span>用户组</span>
                <CaretDownOutlined />
              </Space>
            </a>
          </Dropdown>
          <Button
            className={styles.adminButton}
            icon={<HomeFilled />}
            onClick={() => handleTabClick("进入后台")}
          >
            进入后台
          </Button>
        </div>
      </Header>
    </ConfigProvider>
  );
};

export default Navigation;
