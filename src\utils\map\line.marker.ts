import { divIcon } from "leaflet";

const getLineNameBoxSvg = (lineName: string, scale: number) => {
  return `
  <svg class="align-middle" style="width: ${60 * scale}px; height: ${
    30 * scale
  }px;" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none"
  version="1.1" width="60" height="30" viewBox="0 0 60 30">
  <defs>
    <linearGradient x1="0.5" y1="0" x2="0.5" y2="1" id="${lineName}-master_svg0_223_11185">
      <stop offset="0%" stop-color="var(--color)" stop-opacity="0.30000001192092896" />
      <stop offset="98.57142567634583%" stop-color="var(--color)" stop-opacity="0.5" />
    </linearGradient>
    <radialGradient cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" id="${lineName}-master_svg1_223_11186"
      gradientTransform="translate(30 28.738133311271667) rotate(90) scale(20.488155484199524 55.18258601395516)">
      <stop offset="0%" stop-color="var(--color)" stop-opacity="1" />
      <stop offset="100%" stop-color="var(--color)" stop-opacity="0" />
    </radialGradient>
    <linearGradient x1="0.5" y1="0" x2="0.5" y2="1" id="${lineName}-master_svg2_223_11162">
      <stop offset="0%" stop-color="var(--color)" stop-opacity="0.20000000298023224" />
      <stop offset="100%" stop-color="var(--color)" stop-opacity="0.4000000059604645" />
    </linearGradient>
  </defs>
  <g>
    <g style="opacity:0.5;">
      <rect x="0" y="0" width="60" height="30" rx="6" fill="url(#${lineName}-master_svg0_223_11185)"
        fill-opacity="1" />
      <rect x="0" y="0" width="60" height="30" rx="6" fill="url(#${lineName}-master_svg1_223_11186)"
        fill-opacity="0.5" />
    </g>
    <g>
      <rect x="2" y="2" width="56" height="26" rx="4" fill="#09132E"
        fill-opacity="0.6000000238418579" />
      <rect x="2" y="2" width="56" height="26" rx="4" fill="url(#${lineName}-master_svg2_223_11162)"
        fill-opacity="1" />
      <rect x="2.5" y="2.5" width="55" height="25" rx="3.5" fill-opacity="0" stroke-opacity="1"
        stroke="var(--color)" fill="none" stroke-width="1" />
    </g>
  </g>
</svg>
  `;
};

export function getLineNameMarker(lineName: string, color: string, scale = 1) {
  if (scale === 0) {
    return divIcon({
      html: ``,
      iconSize: [0, 0],
    });
  }
  const width = 60;
  const height = 30;

  const lineNameSvg = getLineNameBoxSvg(lineName, scale);

  return divIcon({
    html: `
       <div class="relative" style="--color: ${color}">
          ${lineNameSvg}
          <div class="absolute w-full h-full top-0 left-0 flex-center text-white" style="width: ${
            60 * scale
          }px; height: ${30 * scale}px; line-height: ${
      30 * scale
    }px; font-size: ${14 * scale}px;">
           ${lineName}
          </div>
        </div>
    `,
    className: "active-effect",
    iconSize: [width * scale, height * scale],
    iconAnchor: [(width * scale) / 2, 1.25 * height * scale],
  });
}
