$primary-bg: #1f375b;
$secondary-bg: #0c4d87;
$text-gradient: linear-gradient(180deg, #ffffff 0%, #90deff 100%);
$button-gradient: linear-gradient(
  270deg,
  rgba(30, 190, 252, 0) 0%,
  #0d89f7 100%
);
$border-radius: 4px;
$spacing-sm: 5px;
$spacing-md: 10px;
$spacing-lg: 15px;
$font-size-sm: 16px;
$font-size-md: 18px;
$font-size-lg: 20px;
$font-size-xl: 24px;

@mixin scrollbar-styles {
  &::-webkit-scrollbar {
    width: 6px;
    background: $primary-bg;
  }

  &::-webkit-scrollbar-thumb {
    background: $secondary-bg;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background: $primary-bg;
  }
}

@mixin background-image($url) {
  background-image: url($url);
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}

@mixin text-gradient-style {
  background-image: $text-gradient;
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

// 主样式
.recentInquiries {
  border-radius: $border-radius;
  height: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: $spacing-md;
    @include background-image("@/assets/images/home/<USER>");
}

// 列表样式
.list {
  width: 100%;
  flex: 3;
  min-height: 0;
  margin-top: 0;
}

.chartTitle {
  width: 100%;
  height: 45px;
  @include background-image("@/assets/images/home/<USER>");

  span {
    font-family: "PingFangSC", "PingFang SC";
    font-weight: 400;
    font-size: $font-size-xl;
    line-height: 40px;
    text-align: justify;
    font-style: normal;
    margin-left: 37px;
    @include text-gradient-style;
  }
}

.listItemContainer {
  width: 100%;
  height: calc(100% - 50px - 54px);
  overflow-y: auto;
  @include scrollbar-styles;
}

.listBgImg {
  width: 100%;
  height: 45px;
  padding: 0 $spacing-sm;
  margin: $spacing-md 0;
  @include background-image("@/assets/images/home/<USER>");
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 400;
  font-size: $font-size-md;
  color: #ffffff;
}

// 等待
.waiting {
  width: 100%;
  flex: 2;
  min-height: 0;
  margin-top: 0;
}

// 详情模块样式
.detail {
  width: 100%;
  height: 100%;
}

.detailTitle {
  width: 100%;
  height: 40px;
  @include text-gradient-style;
  font-weight: 400;
  font-size: $font-size-lg;
}

.detailContent {
  height: calc(100% - 45px - 54px - 50px);
  overflow-y: auto;
  @include scrollbar-styles;
}

.detailItem {
  width: 100%;
  height: 200px;
  @include background-image("@/assets/images/home/<USER>");
}

.itemTop {
  width: 100%;
  height: 25%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.itemContent {
  width: 100%;
  height: 75%;
}

.itemContentTitle {
  width: 100%;
  height: 50%;
  font-size: $font-size-sm;
  color: #ffffff;
  padding: $spacing-sm $spacing-md;
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid $secondary-bg;
}

.itemContentTitleText {
  width: 75%;
  height: 100%;
  padding: $spacing-sm $spacing-md;
  overflow-y: auto;
  -ms-overflow-style: none;
  scrollbar-width: none;

  &::-webkit-scrollbar {
    width: 0;
    background: $primary-bg;
  }
}

.itemContentDesc {
  width: 100%;
  height: 50%;
  padding: $spacing-sm $spacing-lg;
  font-size: $font-size-sm;
  color: #ffffff;
  overflow-y: auto;
  @include scrollbar-styles;
}
