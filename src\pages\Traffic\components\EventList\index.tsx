import React, { useState } from "react";
import { IMAGES } from "../../images";
import styles from "./index.module.scss";

const EventList: React.FC = () => {
  // 状态管理选中的筛选条件
  const [dateFeature, setDateFeature] = useState("全部");
  const [eventAttribution, setEventAttribution] = useState("全部");
  const [maxDelay, setMaxDelay] = useState("全部");
  const [selectedEventId, setSelectedEventId] = useState<number | null>(null);

  // Mock事件数据
  const eventData = [
    {
      id: 1,
      title: "0504车辆故障救援事件",
      level: "一般A类事故",
      nature: "自然灾害类",
      line: "浦江线",
      dateFeature: "工作日",
      reason: "车辆故障",
      delay: "15分钟以上",
      handling: "处置不足",
    },
    {
      id: 2,
      title: "0506信号系统异常事件",
      level: "一般B类事故",
      nature: "设备故障类",
      line: "1号线",
      dateFeature: "双休日",
      reason: "通号故障",
      delay: "5分钟以上",
      handling: "处置不足",
    },
    {
      id: 3,
      title: "0508供电系统故障事件",
      level: "一般A类事故",
      nature: "设备故障类",
      line: "2号线",
      dateFeature: "节假日",
      reason: "供电故障",
      delay: "30分钟以上",
      handling: "处置充分",
    },
    {
      id: 4,
      title: "0508供电系统故障事件",
      level: "一般A类事故",
      nature: "设备故障类",
      line: "2号线",
      dateFeature: "节假日",
      reason: "供电故障",
      delay: "30分钟以上",
      handling: "处置充分",
    },
    {
      id: 5,
      title: "0508供电系统故障事件",
      level: "一般A类事故",
      nature: "设备故障类",
      line: "2号线",
      dateFeature: "节假日",
      reason: "供电故障",
      delay: "30分钟以上",
      handling: "处置充分",
    },
    {
      id: 6,
      title: "0508供电系统故障事件",
      level: "一般A类事故",
      nature: "设备故障类",
      line: "2号线",
      dateFeature: "节假日",
      reason: "供电故障",
      delay: "30分钟以上",
      handling: "处置充分",
    },
    {
      id: 7,
      title: "0508供电系统故障事件",
      level: "一般A类事故",
      nature: "设备故障类",
      line: "2号线",
      dateFeature: "节假日",
      reason: "供电故障",
      delay: "30分钟以上",
      handling: "",
    },
  ];

  return (
    <div className={styles.eventList}>
      <div className={styles.eventListTitle}>
        <div className={styles.filterGroup}>
          <span className={styles.filterLabel}>日期特征：</span>
          {["全部", "工作日", "双休日", "节假日"].map((item) => (
            <button
              key={item}
              className={`${styles.filterButton} ${
                dateFeature === item ? styles.active : ""
              }`}
              onClick={() => setDateFeature(item)}
            >
              {item}
            </button>
          ))}
        </div>
        <div className={styles.filterGroup}>
          <span className={styles.filterLabel}>事件归因：</span>
          {["全部", "车辆", "通号", "供电", "工务", "调度", "客运", "客观"].map(
            (item) => (
              <button
                key={item}
                className={`${styles.filterButton} ${
                  eventAttribution === item ? styles.active : ""
                }`}
                onClick={() => setEventAttribution(item)}
              >
                {item}
              </button>
            )
          )}
        </div>
        <div className={styles.filterGroup}>
          <span className={styles.filterLabel}>最大晚点：</span>
          {[
            "全部",
            "2分钟以上",
            "5分钟以上",
            "15分钟以上",
            "30分钟以上",
            "其他",
          ].map((item) => (
            <button
              key={item}
              className={`${styles.filterButton} ${
                maxDelay === item ? styles.active : ""
              }`}
              onClick={() => setMaxDelay(item)}
            >
              {item}
            </button>
          ))}
        </div>
      </div>
      <div className={styles.eventListContent}>
        {eventData.map((event) => (
          <div
            key={event.id}
            className={`${styles.eventListContentTitle} ${
              selectedEventId === event.id ? styles.selected : ""
            }`}
            onClick={() => {
              setSelectedEventId(
                event.id === selectedEventId ? null : event.id
              );
            }}
          >
            <div className={styles.contentTitleText}>{event.title}</div>
            <div className={styles.contentText}>
              <div className={styles.contentTextLeft}>
                <img src={IMAGES.cart} alt="" />
              </div>
              <div className="w-[60%] px-4">
                <div className={styles.contentTextTitle}>
                  <button className={styles.soButton}>{event.level}</button>
                  <button className={styles.nature}>{event.nature}</button>
                </div>
                <div className={styles.contentTextContent}>
                  <div className={styles.contentTextP}>
                    所属线路：
                    <span className={styles.contentTextSpan}>{event.line}</span>
                  </div>
                  <div className={styles.contentTextP}>
                    日期特征：
                    <span className={styles.contentTextSpan}>
                      {event.dateFeature}
                    </span>
                  </div>
                  <div className={styles.contentTextP}>
                    事件原因：
                    <span className={styles.contentTextSpan}>
                      {event.reason}
                    </span>
                  </div>
                  <div className={styles.contentTextP}>
                    最大晚点：
                    <span className={styles.contentTextSpan}>
                      {event.delay}
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div
              className={styles.topRight}
              style={{
                backgroundImage:
                  event.handling === "处置不足"
                    ? `url(${IMAGES.insufficientDisposal})`
                    : event.handling === "处置充分"
                    ? `url(${IMAGES.outstanding})`
                    : `url(${IMAGES.noImg})`,
              }}
            >
              {event.handling === "处置不足" && (
                <img src={IMAGES.insufficient} />
              )}
              {event.handling === "处置充分" && (
                <img src={IMAGES.outstandingIcon} />
              )}
              <span className="pb-0.5 px-1">{event.handling}</span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default EventList;
