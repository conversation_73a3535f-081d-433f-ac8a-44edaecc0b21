import { CaretDownOutlined, CaretUpOutlined } from "@ant-design/icons";
import { Select } from "antd";
import React from "react";
import styles from "./index.module.scss";

export const CustomSelect = ({
  defaultValue,
  options,
  onOpenChange,
  selectOpen,
  style,
}: {
  defaultValue: string;
  options: { value: string; label: string }[];
  onOpenChange: (open: boolean) => void;
  selectOpen: boolean;
  style?: React.CSSProperties;
}) => (
  <Select
    defaultValue={defaultValue}
    style={{ ...style, color: "#fff" }}
    onChange={(value) => console.log("Selected:", value)}
    onOpenChange={onOpenChange}
    suffixIcon={
      selectOpen ? (
        <CaretUpOutlined className={styles.selectIcon} />
      ) : (
        <CaretDownOutlined className={styles.selectIcon} />
      )
    }
  >
    {options.map((option) => (
      <Select.Option key={option.value} value={option.value}>
        {option.label}
      </Select.Option>
    ))}
  </Select>
);
