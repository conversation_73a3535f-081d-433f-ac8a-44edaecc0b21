import React, { useState } from "react"; // 添加useState导入
import styles from "./index.module.scss";
import { <PERSON><PERSON>, Col, ConfigProvider, Row } from "antd";
import { IMAGES } from "../../images/index";
import { RightOutlined } from "@ant-design/icons";

interface RecentInquiriesProps {
  onClose?: () => void;
}
const RecentInquiries: React.FC<RecentInquiriesProps> = ({ onClose }) => {
  const [selectedItem, setSelectedItem] = useState<number | null>(null); // 添加状态管理
  const mockData = [
    {
      id: 1,
      name: "协调联动不足",
      theme: "调度作业协调",
      data: [
        {
          reason:
            "各部门之间信息共享不及时，缺乏有效的沟通机制，导致应急处置效率低下",
          suggestion:
            "建立定期沟通机制，明确各部门职责，优化信息传递流程，制定联动处置预案",
        },
        {
          reason: "协调机制不完善，缺乏有效的协调机制，导致协调效率低下",
          suggestion: "完善协调机制，建立协调机制，优化协调流程，提高协调效率",
        },
      ],
    },
    { id: 2, name: "运营调整不合理", theme: "运营管理", data: [] },
    { id: 3, name: "信息传递不到位", theme: "信息管理", data: [] },
    { id: 4, name: "ATS操作不规范", theme: "操作规范", data: [] },
    { id: 5, name: "安全把控不到位", theme: "安全管理", data: [] },
    { id: 6, name: "时间点把控不到位", theme: "时间管理", data: [] },
    { id: 7, name: "命令发布不规范", theme: "命令管理", data: [] },
  ];

  // 添加点击处理函数
  const handleItemClick = (id: number) => {
    setSelectedItem(id);
  };

  return (
    <ConfigProvider
      theme={{
        components: {
          Button: {
            defaultBg:
              "linear-gradient( 270deg, rgba(30,190,252,0) 0%, #0D89F7 100%);",
            defaultBorderColor: "none",
            defaultActiveBg:
              "linear-gradient( 270deg, rgba(30,190,252,0) 0%, #0D89F7 100%);",
            defaultHoverBg:
              "linear-gradient( 270deg, rgba(30,190,252,0) 0%, #0D89F7 100%);",
            defaultHoverBorderColor: "none",
            defaultColor: "#90E8FF",
            defaultHoverColor: "#fff ",
          },
        },
      }}
    >
      <div className={styles.recentInquiries}>
        {!selectedItem && (
          <>
            <div className={styles.list}>
              <div className={styles.chartTitle}>
                <span>调度作业对策库</span>
              </div>
              <Row style={{ padding: "8px 0" }}>
                <Col span={4} style={{ display: "flex", alignItems: "center" }}>
                  <img src={IMAGES.retracement} alt="" onClick={onClose} />
                </Col>
                <Col
                  span={20}
                  style={{ display: "flex", alignItems: "center" }}
                >
                  <div
                    style={{
                      backgroundImage:
                        "linear-gradient(180deg, #ffffff 0%, #90deff 100%)",
                      WebkitBackgroundClip: "text",
                      backgroundClip: "text",
                      color: "transparent",
                      fontSize: "18px",
                      fontWeight: 400,
                      width: "100%",
                      textAlign: "center",
                    }}
                  >
                    请选择你想要了解的处置不足类型？
                  </div>
                </Col>
              </Row>
              <div className={styles.listItemContainer}>
                {mockData.map((item) => (
                  <div
                    className={styles.listBgImg}
                    key={item.id}
                    onClick={() => handleItemClick(item.id)}
                  >
                    <img src={IMAGES.listbgIcon} alt="" />
                    <span>{item.name}</span>
                    <img src={IMAGES.listbgIcon2} alt="" />
                  </div>
                ))}
              </div>
            </div>
            <div className={styles.waiting}></div>
          </>
        )}

        {/* 详情模块 */}
        {selectedItem && (
          <div className={styles.detail}>
            <div className={styles.chartTitle}>
              <span>调度作业对策库</span>
            </div>
            <Row style={{ padding: "8px 0" }}>
              <Col span={4} style={{ display: "flex", alignItems: "center" }}>
                <img
                  src={IMAGES.retracement}
                  alt=""
                  onClick={() => setSelectedItem(null)}
                />
              </Col>
              <Col span={20} style={{ display: "flex", alignItems: "center" }}>
                <div
                  style={{
                    backgroundImage:
                      "linear-gradient(180deg, #ffffff 0%, #90deff 100%)",
                    WebkitBackgroundClip: "text",
                    backgroundClip: "text",
                    color: "transparent",
                    fontSize: "18px",
                    fontWeight: 400,
                    width: "100%",
                    textAlign: "center",
                  }}
                >
                  {mockData.find((item) => item.id === selectedItem)?.name}
                </div>
              </Col>
            </Row>
            <div className={styles.detailTitle}>
              对策库已为您查询到35条相关处置不足条目
            </div>
            <div className={styles.detailContent}>
              {mockData
                .find((item) => item.id === selectedItem)
                ?.data?.map((dataItem, index) => (
                  <div className={styles.detailItem} key={index}>
                    <div className={styles.itemTop}>
                      <div className="text-light w-[12%] flex justify-center items-center font-500 text-[28px]">
                        {index + 1}
                      </div>
                      <div className="text-[#D4E9FF] w-[87%] text-[20px] font-500">
                        {
                          mockData.find((item) => item.id === selectedItem)
                            ?.theme
                        }
                      </div>
                    </div>
                    <div className={styles.itemContent}>
                      <div className={styles.itemContentTitle}>
                        <div className={styles.itemContentTitleText}>
                          {dataItem.reason}
                        </div>
                        <div className="w-[25%] h-full flex justify-right items-end">
                          <Button
                            style={{ height: "70%", opacity: "0.6" }}
                            icon={<RightOutlined />}
                          >
                            查看案例
                          </Button>
                        </div>
                      </div>
                      <div className={styles.itemContentDesc}>
                        <img src={IMAGES.bulbIcon} alt="" />
                        {dataItem.suggestion}
                      </div>
                    </div>
                  </div>
                ))}
            </div>
          </div>
        )}
      </div>
    </ConfigProvider>
  );
};

export default RecentInquiries;
