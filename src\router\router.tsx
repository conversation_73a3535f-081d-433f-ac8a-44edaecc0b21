import Contrast from "@/pages/Contrast";
import Regulations from "@/pages/Regulations";
import Resources from "@/pages/Resources";
import Subject from "@/pages/Subject";
import React, { Suspense } from "react";
import { RouteObject } from "react-router-dom";
import DotSpinner from "../components/DotSpinner";
import DashboardScreen from "../pages/DashboardScreen";

const Home = React.lazy(() => import("../pages/Home"));
const Login = React.lazy(() => import("../pages/Login"));
const Demo = React.lazy(() => import("../pages/Demo"));
const Portal = React.lazy(() => import("@/pages/Portal"));
const Traffic = React.lazy(() => import("@/pages/Traffic"));

/**
 * 公共路由配置
 */
export const constantRoutes: RouteObject[] = [
  {
    path: "/login",
    id: "Login",
    element: (
      <>
        <Suspense fallback={<DotSpinner />}>
          <Login />
        </Suspense>
      </>
    ),
  },
  {
    path: "/404",
    id: "NotFound",
    element: (
      <>
        <Suspense fallback={<DotSpinner />}>
          <Home />
        </Suspense>
      </>
    ),
  },
  {
    path: "/demo",
    id: "demo",
    element: <Demo />,
  },
  {
    path: "/dashboardScreen",
    id: "DashboardScreen",
    element: <DashboardScreen />,
  },
  {
    path: "/",
    id: "Home",
    element: (
      <Suspense fallback={<DotSpinner />}>
        <Subject />
      </Suspense>
    ),
    children: [
      {
        path: "portal",
        element: <Portal />,
      },
      {
        path: "traffic",
        element: <Traffic />,
      },
    ],
  },
  {
    path: "/resources",
    id: "Resources",
    element: <Resources />,
    children: [
      {
        path: "contrast",
        element: <Contrast />,
      },
      {
        path: "regulations",
        element: <Regulations />,
      },
    ],
  },
];
