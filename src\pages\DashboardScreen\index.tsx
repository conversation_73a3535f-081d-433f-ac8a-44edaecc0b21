import { Layout } from "antd";
import React, { useState } from "react";
import styles from "./index.module.scss";
// 引入ECharts组件
import AttributionInquiries from "./components/AttributionInquiries";
import Bar<PERSON>hart from "./components/BarChart";
import NotSddList from "./components/NotSddList";
import <PERSON><PERSON><PERSON> from "./components/PieChart";
import RecentInquiries from "./components/RecentInquiries/RecentInquiries";
import Revolve from "./components/Revolve";
import { IMAGES } from "./images";
const { Header, Content } = Layout;

const DashboardScreen: React.FC = () => {
  const [showInquiries, setShowInquiries] = useState(false);
  const [showAttribution, setShowAttribution] = useState(false);
  const [showQueryModule, setShowQueryModule] = useState(true);

  const handleInsufficientDisposalClick = () => {
    setShowInquiries(true);
    setShowAttribution(false);
    setShowQueryModule(false);
  };

  const handleAttributionClick = () => {
    setShowInquiries(false);
    setShowAttribution(true);
    setShowQueryModule(false);
  };

  return (
    <Layout className={styles.dashboardContainer}>
      <Header className={styles.dashboardHeader}>
        <span className={styles.headerTitle}>上海申通运营智汇数据平台</span>
      </Header>
      <Content className={styles.dashboardContent}>
        <div className={styles.contentContainer}>
          <div className={styles.statisticsArea}>
            <div className={styles.statisticsTitle}>
              <div className="w-[50%] absolute top-[-25%] left-0 flex items-center justify-center">
                <img src={IMAGES.LowIcon} alt="" />
              </div>
              <div className="w-[50%] absolute top-[-25%] right-0 flex flex-col items-center">
                <div className={styles.mainNumber}>385</div>
                <div className={styles.cardLabel}>总计录入案例数</div>
              </div>
            </div>
            <BarChart />
            <PieChart />
          </div>
          <div className={styles.centralVisualization}>
            <Revolve />
            <NotSddList />
          </div>
          <div className={styles.sidebar}>
            {showQueryModule && (
              <div className={styles.queryModule}>
                <div className={styles.chartTitle}>
                  <span>重大案例统计</span>
                </div>
                <div className={styles.queryArea}>
                  <div
                    className={styles.queryAreaImg}
                    onClick={handleInsufficientDisposalClick}
                  >
                    <span>处置不足查询</span>
                  </div>
                  <div
                    className={styles.queryAreaImg}
                    onClick={handleAttributionClick}
                  >
                    <span>归因查询</span>
                  </div>
                </div>
              </div>
            )}
            {showQueryModule && <div className={styles.moduleSelector}></div>}
            {showInquiries && (
              <RecentInquiries
                onClose={() => {
                  setShowInquiries(false);
                  setShowQueryModule(true);
                }}
              />
            )}
            {showAttribution && <AttributionInquiries />}
          </div>
        </div>
      </Content>
    </Layout>
  );
};

export default DashboardScreen;
