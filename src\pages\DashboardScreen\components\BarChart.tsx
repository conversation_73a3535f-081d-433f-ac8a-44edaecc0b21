import React from "react";
import ReactECharts from "echarts-for-react";
import * as echarts from "echarts";
import styles from "../index.module.scss";

const BarChart: React.FC = () => {
  const chartOption = {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      data: ["车辆", "供电", "供电", "公务", "调度", "客运", "客观"],
      axisLabel: {
        rotate: 0,
        interval: 0,
        align: "center",
      },
      axisTick: {
        alignWithLabel: true,
      },
    },
    yAxis: {
      type: "value",
      axisLabel: {
        show: false,
      },
      axisLine: {
        show: false,
      },
      splitLine: {
        show: false,
      },
    },
    series: [
      {
        data: [30, 22, 32, 28, 26, 25, 42],
        type: "bar",
        barWidth: 18,
        label: {
          show: true,
          position: "top",
          color: "#E4F3FF",
          fontSize: 16,
        },
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: "#00FFFF" },
            { offset: 1, color: "rgba(105,121,255,0)" },
          ]),
          borderRadius: [12, 12, 0, 0],
        },
        emphasis: {
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: "#00FFFF" },
              { offset: 1, color: "rgba(105,121,255,0)" },
            ]),
          },
          label: {
            color: "color: #00F8F4;",
            fontSize: 20,
          },
        },
      },
    ],
  };

  return (
    <div className={styles.chartContainer}>
      <div className={styles.chartTitle}>
        <span>案例归因统计</span>
      </div>
      <div className={styles.chartBackground}>
        <ReactECharts option={chartOption} opts={{ renderer: "canvas" }} />
      </div>
    </div>
  );
};

export default BarChart;
