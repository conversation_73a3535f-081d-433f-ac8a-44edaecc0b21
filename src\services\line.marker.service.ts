import { getLineNameMarker } from "@/utils/map/line.marker";
import { getLineNameAlias, getLinePrimaryColor } from "@/utils/map/meta";
import { MetroLine } from "@/utils/map/metro.line.class";
import { clearGlobalStationToLineMap } from "@/utils/map/metro.station.class";
import {
  FeatureGroup,
  LatLngExpression,
  Map as LeafletMap,
  marker,
  Marker,
} from "leaflet";

const markerSizeMap = [
  { scale: 0, offset: 0 }, // 09
  { scale: 0.75, offset: 0 }, // 10
  { scale: 1, offset: 0 }, // 11
  { scale: 0, offset: 20 }, // 12
  { scale: 0, offset: 35 }, // 13
  { scale: 0, offset: 40 }, // 14
  { scale: 0, offset: 45 }, // 15
  { scale: 0, offset: 45 }, // 16
];

interface MarkerMapItem {
  line: string;
  alias: string;
  visible: boolean;
  marker?: Marker;
  position: LatLngExpression;
  positionString: string;
  primaryColor: string;
}

const positionAdjustment: Record<string, string> = {
  "5号线": "奉贤新城站",
  "9号线": "上海松江站站",
  "11号线": "花桥站",
  "17号线": "东方绿舟站",
  浦江线: "汇臻路站",
  机场联络线: "虹桥2号航站楼",
  磁浮线: "龙阳路站",
};

export class LineEffectService {
  initiated = false;
  map!: LeafletMap;
  featureGroup = new FeatureGroup([]);

  lineModels: MetroLine[] = [];
  lineNameMarkers: MarkerMapItem[] = [];

  mount(map: LeafletMap, lineModels: MetroLine[]) {
    this.map = map;
    this.lineModels = lineModels;

    this.featureGroup.addTo(this.map);
    this.map.on("zoomend", () => {
      this.effectWithZoom();
    });
    this.map.on("click", (_ev) => {
      // this.onMapClick(ev);
    });
    this.initializeMarkers();
  }

  effectWithZoom() {
    if (!this.lineNameMarkers.length) return;
    // const zoom = this.map.getZoom();
    // const currentIsRemote = zoom < this.remoteDistinctZoom;
    this.rerenderAllMarkers();
  }

  rerenderAllMarkers() {
    const zoom = this.map.getZoom();
    const { scale } = this.getMarkerSize(zoom);
    this.lineNameMarkers.forEach((item) => {
      const { marker } = item;
      if (marker) {
        marker.setIcon(getLineNameMarker(item.alias, item.primaryColor, scale));
      }
    });
  }
  initializeMarkers() {
    this.lineNameMarkers = this.lineModels.map((m) => {
      let locateStation = m.stations[0]!;
      if (positionAdjustment[m.name]) {
        locateStation = m.findStationByName(positionAdjustment[m.name])!;
      }
      const position = locateStation.meta.coord;
      return {
        line: m.name,
        alias: getLineNameAlias(m.name),
        visible: true,
        position: position as LatLngExpression,
        positionString: position.join(", "),
        primaryColor: getLinePrimaryColor(m.name),
        marker: undefined,
      };
    });

    this.lineNameMarkers.map((d) => {
      d.marker = this.getMarker(d);
      d.marker.addTo(this.featureGroup);
    });
  }

  getMarkerSize(zoom: number) {
    const minZoom = 9;
    return markerSizeMap[zoom - minZoom] || { scale: 0, offset: 0 };
  }

  getMarker(data: MarkerMapItem) {
    const { position } = data;
    const zoom = this.map.getZoom();
    const { scale } = this.getMarkerSize(zoom);

    const m = marker(position as LatLngExpression, {
      icon: getLineNameMarker(data.alias, data.primaryColor, scale),
    }) as Marker;

    return m;
  }

  followVisibility(lines: string[]) {
    this.followVisibilityWithMarkers(lines);
    this.followVisibilityWithModels(lines);
  }

  followVisibilityWithMarkers(lines: string[]) {
    this.lineNameMarkers.forEach((m) => {
      if (lines.includes(m.line)) {
        if (!m.visible) {
          this.revertMarker(m);
        }
      } else {
        if (m.visible) {
          this.hideMarker(m);
        }
      }
    });

    this.lineNameMarkers.forEach((d) => {
      d.visible = lines.includes(d.line);
    });
    this.lineNameMarkers.forEach((d) => {
      if (d.visible) {
        if (!d.marker) {
          const marker = this.getMarker(d);
          this.featureGroup.addLayer(marker);
          d.marker = marker;
        }
      } else {
        d.marker?.remove();
        d.marker = undefined;
      }
    });
  }

  followVisibilityWithModels(lines: string[]) {
    this.lineModels.forEach((l) => {
      l.detachStations();
    });
    clearGlobalStationToLineMap();

    this.lineModels.forEach((l) => {
      if (lines.includes(l.meta.name)) {
        l.attachLine();
      } else {
        l.detachLine();
      }
    });
    this.lineModels
      .filter((l) => lines.includes(l.name))
      .forEach((l) => l.attachStations());
  }

  revertMarker(m: MarkerMapItem) {
    m.visible = true;
    m.marker?.addTo(this.featureGroup);
  }
  hideMarker(m: MarkerMapItem) {
    m.visible = false;
    m.marker?.remove();
  }
}

export const lineEffectService = new LineEffectService();
