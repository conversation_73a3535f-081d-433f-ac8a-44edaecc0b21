import centerIcon from "@/assets/images/regulations/centerIcon.png";
import lineIcon from "@/assets/images/regulations/lineIcon.png";
import { Layout } from "antd";
import React from "react";
import { IMAGES } from "../Traffic/images";
import styles from "./index.module.scss";
import TreeTable from "./TreeTable";

const Regulations: React.FC = () => {
  const { Content } = Layout;
  return (
    <Content className={styles.content}>
      <div className={styles.contentText}>
        <div className={styles.title}>
          <img src={IMAGES.siftIcon} alt="" />
          规章库列表
        </div>
        <div className={styles.headerItem}>
          <div className={styles.item}>
            <div className={styles.itemLeft}>
              <img src={IMAGES.groupImg} alt="" />
              <div>
                <div>集团级</div>
                <div>规章制度</div>
              </div>
            </div>
            <div className={styles.itemRight}>
              <div className="w-[190px] h-[48px] flex items-center justify-evenly">
                <img src={IMAGES.groupIcon} alt="" />
                <span className={styles.leftTitle}>总计数量</span>
                <span className={styles.number}>120</span>
              </div>
            </div>
          </div>
          <div className={styles.itemTwo}>
            <div className={styles.itemLeft}>
              <img src={centerIcon} alt="" />
              <div>
                <div>中心级</div>
                <div>规章制度</div>
              </div>
            </div>
            <div className={styles.itemRightTwo}>
              <div className="w-[190px] h-[48px] flex items-center justify-evenly">
                <img src={IMAGES.groupIcon} alt="" />
                <span className={styles.leftTitle}>总计数量</span>
                <span className={styles.number}>5</span>
              </div>
              <div className="w-[190px] h-[48px] flex items-center justify-evenly">
                <img src={IMAGES.groupIcon} alt="" />
                <span className={styles.leftTitle}>即将失效</span>
                <span className={styles.number}>2</span>
              </div>
            </div>
          </div>
          <div className={styles.itemTwo}>
            <div className={styles.itemLeft}>
              <img src={lineIcon} alt="" />
              <div>
                <div>线路级</div>
                <div>规章制度</div>
              </div>
            </div>
            <div className={styles.itemRightTwo}>
              <div className="w-[190px] h-[48px] flex items-center justify-evenly">
                <img src={IMAGES.groupIcon} alt="" />
                <span className={styles.leftTitle}>总计数量</span>
                <span className={styles.number}>4</span>
              </div>
              <div className="w-[190px] h-[48px] flex items-center justify-evenly">
                <img src={IMAGES.groupIcon} alt="" />
                <span className={styles.leftTitle}>即将失效</span>
                <span className={styles.numbers}>1</span>
              </div>
            </div>
          </div>
        </div>
        <div className={styles.contentTree}>
          <TreeTable />
        </div>
      </div>
    </Content>
  );
};

export default Regulations;
