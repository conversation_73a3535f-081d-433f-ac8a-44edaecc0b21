import {
  CaretDownOutlined,
  CaretUpOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import { Input, Layout, Select } from "antd";
import React, { useState } from "react";
import EventList from "./components/EventList";
import StudyList from "./components/StudyList";
import { IMAGES } from "./images";
import styles from "./index.module.scss";
const Traffic: React.FC = () => {
  const { Search } = Input;
  const { Content } = Layout;
  const [selectedLine, setSelectedLine] = useState<string>("all");
  // 为三个Select组件创建独立的展开状态
  const [select1Open, setSelect1Open] = useState<boolean>(false);
  const [select2Open, setSelect2Open] = useState<boolean>(false);
  const [select3Open, setSelect3Open] = useState<boolean>(false);

  const handleLineSelect = (value: string) => {
    setSelectedLine(value);
  };

  return (
    <Content className={styles.content}>
      <div className={styles.mainContainer}>
        <div className={styles.leftPanel}>
          <div className={styles.leftPanelTitle}>
            <div className={styles.leftPanelTitleLeft}>
              <img src={IMAGES.siftIcon} alt="" />
              案例筛选
            </div>
            <div className="w-[70%] flex justify-around items-center">
              <Select
                defaultValue="all"
                style={{ width: 180, color: "#fff" }}
                onChange={handleLineSelect}
                onOpenChange={(open) => setSelect1Open(open)}
                suffixIcon={
                  select1Open ? (
                    <CaretUpOutlined className={styles.selectIcon} />
                  ) : (
                    <CaretDownOutlined className={styles.selectIcon} />
                  )
                }
              >
                <Select.Option value="all">全部年份</Select.Option>
                <Select.Option value="fullStack">2025年</Select.Option>
                <Select.Option value="eighteenth">2024年</Select.Option>
              </Select>
              <Select
                defaultValue="all"
                style={{ width: 180, color: "#fff" }}
                onChange={handleLineSelect}
                onOpenChange={(open) => setSelect2Open(open)}
                suffixIcon={
                  select2Open ? (
                    <CaretUpOutlined className={styles.selectIcon} />
                  ) : (
                    <CaretDownOutlined className={styles.selectIcon} />
                  )
                }
              >
                <Select.Option value="all">全部线路</Select.Option>
                <Select.Option value="fullStack">2025年</Select.Option>
                <Select.Option value="eighteenth">2024年</Select.Option>
              </Select>
              <Select
                defaultValue="all"
                style={{ width: 180, color: "#fff" }}
                onChange={handleLineSelect}
                onOpenChange={(open) => setSelect3Open(open)}
                suffixIcon={
                  select3Open ? (
                    <CaretUpOutlined className={styles.selectIcon} />
                  ) : (
                    <CaretDownOutlined className={styles.selectIcon} />
                  )
                }
              >
                <Select.Option value="all">处置不足类型</Select.Option>
                <Select.Option value="fullStack">测试类型1</Select.Option>
                <Select.Option value="eighteenth">测试类型2</Select.Option>
              </Select>
              <div className={styles.searchWrapper}>
                <input
                  type="text"
                  placeholder="输入搜索关键词"
                  className={styles.searchInput}
                />
                <button className={styles.searchButtonInside}>
                  <SearchOutlined style={{ fontSize: "18px" }} />
                </button>
              </div>
            </div>
          </div>
          <div className={styles.eventList}>
            <EventList />
          </div>
        </div>
        <div className={styles.rightPanel}>
          <StudyList />
        </div>
      </div>
    </Content>
  );
};
export default Traffic;
