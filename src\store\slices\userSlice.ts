import { User<PERSON><PERSON> } from "@/apis/user";
import { createAsyncThunk, createSlice, PayloadAction } from "@reduxjs/toolkit";

interface UserState {
  currentUser: User.IUserInfo | null;
}

const initialState: UserState = {
  currentUser: null,
};

export const fetchUserById = createAsyncThunk(
  "users/fetchByIdStatus",
  async (userId: number) => {
    try {
      const response = await UserApi.getInfoByUserId(userId);
      return response.data;
    } catch (error) {
      return error;
    }
  }
);

const userSlice = createSlice({
  name: "user",
  initialState,
  reducers: {
    setUser: (state, action: PayloadAction<User.IUserInfo>) => {
      state.currentUser = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder.addCase(fetchUserById.fulfilled, (state, action) => {
      state.currentUser = action.payload;
    });
    // builder.addCase(fetchUserById.rejected, (state, action) => {
    //   state.currentUser = null;
    // });
  },
});

export const { setUser } = userSlice.actions;
export default userSlice.reducer;
