@mixin background-image($url) {
  background-image: url($url);
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}
@mixin scrollbar-style {
  &::-webkit-scrollbar {
    width: 6px;
    background: #1f375b;
  }
  &::-webkit-scrollbar-thumb {
    background: #0c4d87;
    border-radius: 3px;
  }
  &::-webkit-scrollbar-track {
    background: #1f375b;
  }
}
.eventList {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .eventListTitle {
    flex: 1;
    width: 100%;
    .filterGroup {
      width: 100%;
      height: 38px;
      display: flex;
      justify-content: left;
      align-items: center;
      .filterLabel {
        margin-right: 8px;
        font-size: 14px;
        white-space: nowrap;
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 16px;
        color: #ffffffda;
      }
      .filterButton {
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 16px;
        color: #ffffff;
        background: none;
        border: none;
        border-radius: 5px;
        margin-left: 5px;
        border: 1px solid rgba(41, 194, 255, 0.007);
      }
      .active {
        background: rgba(35, 65, 147, 0.7);
        border-radius: 4px;
        border: 1px solid rgb(41, 194, 255);
      }
    }
  }
  .eventListContent {
    flex: 5;
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    column-gap: 24px;
    row-gap: 20px;
    overflow-y: auto;
    @include scrollbar-style;
    .eventListContentTitle {
      width: 630px;
      height: 244px;
      color: #fff;
      @include background-image("@/assets/images/traffic/domesticImg.png");
      position: relative;
      &.selected {
        @include background-image(
          "@/assets/images/traffic/domesticBgCheck.png"
        );
      }
      .contentTitleText {
        width: 100%;
        height: 38px;
        font-family: "PingFangSC";
        font-weight: 600;
        font-size: 18px;
        padding: 0 20px;
        display: flex;
        justify-content: left;
        align-items: center;
      }
      .contentText {
        width: 100%;
        display: flex;
        padding-left: 20px;
        padding-top: 22px;
        padding-bottom: 15px;

        .contentTextLeft {
          width: 35%;
          height: 100%;
          padding: 5px;
          @include background-image("@/assets/images/traffic/photobor.png");
          img {
            width: 100%;
            height: 100%;
          }
        }
        .contentTextTitle {
          width: 100%;
          margin-bottom: 8px;
          padding-left: 10px;
          .soButton {
            padding: 2px 8px;
            color: #fff;
            background: rgba(180, 116, 19, 0.76);
            border-radius: 8px 8px 8px 8px;
            border: 2px solid
              rgba(251.96428209543228, 166.7869734764099, 39.02101814746857, 1);
          }
          .nature {
            color: #fff;
            background: rgba(24, 184, 157, 0.815);
            border-radius: 8px 8px 8px 8px;
            border: 2px solid rgba(47.38938130438328, 255, 220.3982511162758, 1);
            margin-left: 10px;
            padding: 2px 8px;
          }
        }
        .contentTextContent {
          font-family: "PingFangSC";
          font-weight: 400;
          font-size: 14px;
          color: rgba(255, 255, 255, 0.85);
          display: flex;
          flex-wrap: wrap;
          gap: 6px;
          .contentTextSpan {
            color: #29c4fb;
          }
          .contentTextP {
            width: 80%;
            height: 28px;
            display: flex;
            align-items: center;
            padding: 0 25px;
            @include background-image("@/assets/images/traffic/pImg.png");
          }
        }
      }
      .topRight {
        width: 116px;
        height: 28px;
        background-size: 100% 100%;
        background-position: center;
        background-repeat: no-repeat;
        position: absolute;
        top: 0;
        right: 0;
        padding: 0 15px;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
}
