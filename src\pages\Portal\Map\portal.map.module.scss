.portalMap {
  position: relative;
  height: calc(100vh - var(--reserve-top-height));
  --animate-duration: 0.5s;
}

:global(.leaflet-control-attribution) {
  display: none;
}

:global(.dark-mode) {
  :global(.leaflet-tile-pane) {
    filter: url("#x-rays") contrast(2) grayscale(0.47) saturate(1.2);
  }

  :global(&.leaflet-safari) {
    :global(.leaflet-tile-pane) {
      filter: url("#x-rays") contrast(2) grayscale(0.47) saturate(1.2);
    }
  }
}

.mapContainer {
  width: 100%;
  height: 100%;
  // -webkit-mask: radial-gradient(
  //   circle,
  //   rgba(0, 0, 0, 1) 0%,
  //   rgba(0, 0, 0, 1) 50%,
  //   transparent 100%
  // );
  // mask: radial-gradient(
  //   circle,
  //   rgba(0, 0, 0, 1) 0%,
  //   rgba(0, 0, 0, 1) 50%,
  //   transparent 100%
  // );
}

:global(.map-dark-bg) {
  background: linear-gradient(
    to right,
    #081835 0%,
    #081835 15%,
    #173f6d 85%,
    #173f6d 100%
  );
}

.map {
  width: 100%;
  height: 100%;
}

:global(.event-popup-title-font) {
  font-weight: 600;
  font-size: 16px;
  line-height: 22px;
  text-shadow: 0px 2px 4px rgba(41, 41, 41, 0.5);
  text-align: left;
  font-style: normal;
  text-transform: none;
  background: linear-gradient(180deg, #ffffff 47%, var(--color-light1) 99%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.heatButtonPos {
  position: absolute;
  // top: var(--header-height);
  top: calc(var(--header-height) - var(--reserve-top-height));
  right: calc(var(--panel-width) + 40px);
  z-index: 800;
}

.heatButton {
  width: 132px;
  height: 40px;
  cursor: pointer;

  :global(.ant-checkbox-checked) {
    :global(.ant-checkbox-inner) {
      background-color: #2acaff;
    }
  }

  :global(.ant-checkbox-inner) {
    border-color: #2acaff88;
    background-color: transparent;
  }

  .buttonFont {
    font-weight: 500;
    font-size: 16px;
    color: #eaf9ff;
  }
}
