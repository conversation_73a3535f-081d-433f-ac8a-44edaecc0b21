import Auth from "@/components/Auth";
import { useEffect } from "react";
import PortalMap from "./Map";
import styles from "./portal.module.scss";

function placeHeaderOnTop() {
  const div = document.querySelector(".ant-layout-header") as HTMLDivElement;
  div.style = `
    position: absolute;
    left: 0;
    z-index: 800;
    background-color: transparent;"`;
  return () => {
    div.style = ``;
  };
}

export default function Portal() {
  useEffect(placeHeaderOnTop, []);

  return (
    <div className={styles.portalPage}>
      <Auth />
      <PortalMap />
    </div>
  );
}
