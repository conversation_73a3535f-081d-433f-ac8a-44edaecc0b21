import Color from "color";
import { divIcon } from "leaflet";

const getEventBoxSvg = (lineName: string, scale: number) => {
  return `
<svg style="width: ${52 * scale}px; height: ${
    104 * scale
  }px;" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none"
  version="1.1" width="52" height="104" viewBox="0 0 52 104">
  <defs>
    <filter id="${lineName}0_223_09593" filterUnits="objectBoundingBox"
      color-interpolation-filters="sRGB" x="-0.07142857142857142" y="-0.125"
      width="1.1428571428571428" height="1.25">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
      <feGaussianBlur stdDeviation="0.5" result="effect1_foregroundBlur" />
    </filter>
    <filter id="${lineName}1_223_09596" filterUnits="objectBoundingBox"
      color-interpolation-filters="sRGB" x="-0.14285714285714285" y="-0.25"
      width="1.2857142857142858" height="1.5">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
      <feGaussianBlur stdDeviation="1" result="effect1_foregroundBlur" />
    </filter>
    <filter id="${lineName}2_223_09600" filterUnits="objectBoundingBox"
      color-interpolation-filters="sRGB" x="-0.007422978399990882" y="-0.013394693002735143"
      width="1.0148459567999817" height="1.0267893860054702">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
      <feGaussianBlur stdDeviation="0.05000000074505806" result="effect1_foregroundBlur" />
    </filter>
    <filter id="${lineName}3_223_09601" filterUnits="objectBoundingBox"
      color-interpolation-filters="sRGB" x="-0.011040638350333386" y="-0.020324799422243797"
      width="1.0220812767006668" height="1.0406495988444875">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
      <feGaussianBlur stdDeviation="0.05000000074505806" result="effect1_foregroundBlur" />
    </filter>
    <filter id="${lineName}4_223_09602" filterUnits="objectBoundingBox"
      color-interpolation-filters="sRGB" x="-1" y="-0.05" width="3" height="1.1">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
      <feGaussianBlur stdDeviation="0.5" result="effect1_foregroundBlur" />
    </filter>
    <linearGradient x1="0.5" y1="0" x2="0.5" y2="1" id="${lineName}5_223_14931">
      <stop offset="0%" stop-color="var(--color)" stop-opacity="0" />
      <stop offset="100%" stop-color="var(--color)" stop-opacity="1" />
    </linearGradient>
    <filter id="${lineName}6_223_09603" filterUnits="objectBoundingBox"
      color-interpolation-filters="sRGB" x="-0.5" y="-0.025" width="2" height="1.05">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
      <feGaussianBlur stdDeviation="0.25" result="effect1_foregroundBlur" />
    </filter>
    <linearGradient x1="0.5" y1="0" x2="0.5" y2="1" id="${lineName}7_223_14931">
      <stop offset="0%" stop-color="var(--color)" stop-opacity="0" />
      <stop offset="100%" stop-color="var(--color)" stop-opacity="1" />
    </linearGradient>
    <linearGradient x1="0.5" y1="0" x2="0.5" y2="1" id="${lineName}8_1_1582">
      <stop offset="0%" stop-color="#FFFFFF" stop-opacity="0" />
      <stop offset="99.99837279319763%" stop-color="#FFFFFF" stop-opacity="1" />
    </linearGradient>
    
    <linearGradient x1="0.5" y1="0" x2="0.5" y2="1" id="${lineName}a_223_14967">
      <stop offset="3.57142873108387%" stop-color="var(--color-light1)" stop-opacity="1" />
      <stop offset="100%" stop-color="var(--color)" stop-opacity="1" />
    </linearGradient>
    <filter id="${lineName}b_223_09607" filterUnits="objectBoundingBox"
      color-interpolation-filters="sRGB" x="-0.16666666666666666" y="-0.16666666666666666"
      width="1.3333333333333333" height="1.3333333333333333">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
      <feGaussianBlur stdDeviation="2" result="effect1_foregroundBlur" />
    </filter>
    <linearGradient x1="0.5" y1="0" x2="0.5" y2="1" id="${lineName}c_223_14448">
      <stop offset="0%" stop-color="var(--color)" stop-opacity="0.4000000059604645" />
      <stop offset="100%" stop-color="var(--color)" stop-opacity="0.699999988079071" />
    </linearGradient>
    <radialGradient cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" id="${lineName}d_223_14449"
      gradientTransform="translate(26.78466796875 51.67144298553467) rotate(90) scale(32.78104877471924 44.14606881116413)">
      <stop offset="0%" stop-color="var(--color)" stop-opacity="0.800000011920929" />
      <stop offset="100%" stop-color="var(--color)" stop-opacity="0" />
    </radialGradient>
    <linearGradient x1="0.5" y1="0.3032324016094208" x2="0.5" y2="1" id="${lineName}e_223_14502">
      <stop offset="0%" stop-color="var(--color)" stop-opacity="0" />
      <stop offset="97.85714149475098%" stop-color="var(--color)" stop-opacity="0.5" />
    </linearGradient>
    <radialGradient cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" id="${lineName}f_223_14503"
      gradientTransform="translate(26.78466796875 56.51268744468689) rotate(90) scale(14.146493673324585 19.051009851182954)">
      <stop offset="0%" stop-color="var(--color)" stop-opacity="0.699999988079071" />
      <stop offset="100%" stop-color="var(--color)" stop-opacity="0" />
    </radialGradient>
    <linearGradient x1="0.5" y1="0" x2="0.5" y2="1" id="${lineName}g_1_1967">
      <stop offset="62.14599609375%" stop-color="#FFFFFF" stop-opacity="0" />
      <stop offset="100%" stop-color="#FFFFFF" stop-opacity="0.33000001311302185" />
    </linearGradient>
    <filter id="${lineName}h_223_09609" filterUnits="objectBoundingBox"
      color-interpolation-filters="sRGB" x="0" y="0" width="1" height="1">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
      <feColorMatrix in="SourceAlpha" type="matrix" result="hardAlpha"
        values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" />
      <feOffset dy="-4" dx="0" />
      <feGaussianBlur stdDeviation="2" />
      <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
      <feColorMatrix type="matrix"
        values="0 0 0 0 0.9019607901573181 0 0 0 0 0 0 0 0 0 0.16470588743686676 0 0 0 0.20000000298023224 0" />
      <feBlend mode="normal" in2="shape" result="effect1_innerShadow" />
    </filter>
    <linearGradient x1="0.5" y1="0" x2="0.5" y2="1" id="${lineName}i_223_14515">
      <stop offset="36.76387369632721%" stop-color="var(--color)" stop-opacity="0" />
      <stop offset="100%" stop-color="var(--color)" stop-opacity="1" />
    </linearGradient>
    <linearGradient x1="0.5" y1="0" x2="0.5" y2="1" id="${lineName}j_1_1954">
      <stop offset="0%" stop-color="#FFFFFF" stop-opacity="0" />
      <stop offset="100%" stop-color="#FFFFFF" stop-opacity="0.8999999761581421" />
    </linearGradient>
    <linearGradient x1="0.5" y1="0" x2="0.5" y2="1" id="${lineName}k_223_14471">
      <stop offset="57.85714387893677%" stop-color="var(--color-light2)" stop-opacity="0.8999999761581421" />
      <stop offset="100%" stop-color="var(--color-light3)" stop-opacity="0" />
    </linearGradient>
    <linearGradient x1="0.5" y1="0" x2="0.5" y2="1" id="${lineName}l_223_11550">
      <stop offset="0%" stop-color="var(--color)" stop-opacity="0.30000001192092896" />
      <stop offset="98.57142567634583%" stop-color="var(--color)" stop-opacity="0.5" />
    </linearGradient>
    <radialGradient cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" id="${lineName}m_223_11551"
      gradientTransform="translate(26.78466796875 49.83969187736511) rotate(90) scale(30.0492947101593 40.46722974356712)">
      <stop offset="0%" stop-color="var(--color)" stop-opacity="1" />
      <stop offset="100%" stop-color="var(--color)" stop-opacity="0" />
    </radialGradient>
    <linearGradient x1="0.5" y1="0" x2="0.5" y2="1" id="${lineName}n_223_14395">
      <stop offset="0%" stop-color="var(--color)" stop-opacity="1" />
      <stop offset="100%" stop-color="var(--color-pure)" stop-opacity="0.699999988079071" />
    </linearGradient>
    <filter id="${lineName}o_223_09615" filterUnits="objectBoundingBox"
      color-interpolation-filters="sRGB" x="-0.18181818181818182" y="-0.18181818181818182"
      width="1.3636363636363635" height="1.3636363636363635">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
      <feGaussianBlur stdDeviation="2" result="effect1_foregroundBlur" />
    </filter>
    <linearGradient x1="0.5" y1="0" x2="0.5" y2="1" id="${lineName}p_223_14383">
      <stop offset="0%" stop-color="var(--color)" stop-opacity="1" />
      <stop offset="100%" stop-color="var(--color)" stop-opacity="1" />
    </linearGradient>
  </defs>
  <g>
    <g>
      <g>
        <g>
          <path
            d="M26.78466796875,102.6904296875Q27.12836796875,102.6904296875,27.47156796875,102.6808296875Q27.81486796875,102.6711296875,28.15686796875,102.6519296875Q28.49896796875,102.6326296875,28.83886796875,102.6038296875Q29.17886796875,102.5750296875,29.51596796875,102.5367296875Q29.85296796875,102.49842968749999,30.18636796875,102.4506296875Q30.51976796875,102.4029296875,30.84866796875,102.3459296875Q31.17756796875,102.2889296875,31.50116796875,102.2228296875Q31.82466796875,102.1566296875,32.142267968750005,102.0814296875Q32.45976796875,102.0063296875,32.77046796875,101.9223296875Q33.08116796875,101.83832968749999,33.384267968749995,101.7458296875Q33.687367968749996,101.6532296875,33.98206796875,101.5522296875Q34.27686796875,101.45132968749999,34.56266796875,101.3422296875Q34.84836796875,101.2330296875,35.12446796875,101.1161296875Q35.40046796875,100.9991296875,35.66616796875,100.8745296875Q35.93186796875,100.7499296875,36.18646796875,100.6180296875Q36.44116796875,100.4861296875,36.68416796875,100.34732968750001Q36.92716796875,100.2084296875,37.15796796875,100.0629296875Q37.38876796875,99.9174296875,37.60676796875,99.7655296875Q37.82486796875,99.6137296875,38.02956796875,99.4560296875Q38.234267968750004,99.2983296875,38.42526796875,99.1350296875Q38.61616796875,98.9717296875,38.79286796875,98.8032296875Q38.96956796875,98.6348296875,39.131567968750005,98.4616296875Q39.29356796875,98.2884296875,39.44046796875,98.1108296875Q39.587467968750005,97.9333296875,39.71896796875,97.7519296875Q39.850467968749996,97.5704296875,39.96626796875,97.3855296875Q40.082067968749996,97.2006296875,40.18186796875,97.0127296875Q40.28156796875,96.8247296875,40.36506796875,96.6342696875Q40.44856796875,96.4437696875,40.51566796875,96.2511496875Q40.58266796875,96.0585396875,40.63316796875,95.8642696875Q40.68356796875,95.6700096875,40.71726796875,95.47456968750001Q40.750967968750004,95.2791196875,40.76776796875,95.0829696875Q40.78466796875,94.8868196875,40.78466796875,94.6904296875Q40.78466796875,94.4940396875,40.76776796875,94.2978896875Q40.750967968750004,94.1017396875,40.71726796875,93.9062896875Q40.68356796875,93.7108496875,40.63316796875,93.5165896875Q40.58266796875,93.3223196875,40.51566796875,93.1297096875Q40.44856796875,92.9370896875,40.36506796875,92.7465896875Q40.28156796875,92.5560796875,40.18186796875,92.3681496875Q40.082067968749996,92.1802196875,39.96626796875,91.9953096875Q39.850467968749996,91.8103996875,39.71896796875,91.6289596875Q39.587467968750005,91.4475196875,39.44046796875,91.2699896875Q39.29356796875,91.0924596875,39.131567968750005,90.9192596875Q38.96956796875,90.7460596875,38.79286796875,90.5776096875Q38.61616796875,90.4091596875,38.42526796875,90.2458696875Q38.234267968750004,90.0825796875,38.02956796875,89.9248396875Q37.82486796875,89.7670896875,37.60676796875,89.6152796875Q37.38876796875,89.4634696875,37.15796796875,89.31795968750001Q36.92716796875,89.1724396875,36.68416796875,89.0335796875Q36.44116796875,88.8947096875,36.18646796875,88.7628196875Q35.93186796875,88.6309296875,35.66616796875,88.5063496875Q35.40046796875,88.3817596875,35.12446796875,88.2647696875Q34.84836796875,88.1477796875,34.56266796875,88.0386696875Q34.27686796875,87.9295696875,33.98206796875,87.8285996875Q33.687367968749996,87.7276396875,33.384267968749995,87.6350596875Q33.08116796875,87.5424826875,32.77046796875,87.4585156875Q32.45976796875,87.3745476875,32.142267968750005,87.2993936875Q31.82466796875,87.2242386875,31.50116796875,87.1580766875Q31.17756796875,87.0919156875,30.84866796875,87.0349066875Q30.51976796875,86.9778986875,30.18636796875,86.9301796875Q29.85296796875,86.8824606875,29.51596796875,86.8441476875Q29.17886796875,86.8058336875,28.83886796875,86.7770175875Q28.49896796875,86.7482013875,28.15686796875,86.7289518875Q27.81486796875,86.7097023875,27.47156796875,86.7000660375Q27.12836796875,86.6904296875,26.78466796875,86.6904296875Q26.44096796875,86.6904296875,26.09776796875,86.7000660375Q25.75446796875,86.7097023875,25.41246796875,86.7289518875Q25.07036796875,86.7482013875,24.73046796875,86.7770175875Q24.390467968750002,86.8058336875,24.053367968750003,86.8441476875Q23.71636796875,86.8824606875,23.382967968750002,86.9301796875Q23.04956796875,86.9778986875,22.72068796875,87.0349066875Q22.391797968749998,87.0919156875,22.068207968750002,87.1580766875Q21.74461796875,87.2242386875,21.427097968749997,87.2993936875Q21.10957796875,87.3745476875,20.798897968749998,87.4585156875Q20.48820796875,87.5424826875,20.18511796875,87.6350596875Q19.88201796875,87.7276396875,19.58722796875,87.8285996875Q19.29244796875,87.9295696875,19.00668796875,88.0386696875Q18.72092796875,88.1477796875,18.44487796875,88.2647696875Q18.16882796875,88.3817596875,17.90315796875,88.5063496875Q17.63749796875,88.6309296875,17.38283796875,88.7628196875Q17.128187968749998,88.8947096875,16.88516796875,89.0335796875Q16.64215796875,89.1724396875,16.41134796875,89.31795968750001Q16.18054796875,89.4634696875,15.96251796875,89.6152796875Q15.74448796875,89.7670896875,15.53975796875,89.9248396875Q15.33502796875,90.0825796875,15.14409796875,90.2458696875Q14.95315796875,90.4091596875,14.77646796875,90.5776096875Q14.59977796875,90.7460596875,14.43776796875,90.9192596875Q14.27575796875,91.0924596875,14.128817968749999,91.2699896875Q13.98187796875,91.4475196875,13.85035796875,91.6289596875Q13.71883296875,91.8103996875,13.60305096875,91.9953096875Q13.48726796875,92.1802196875,13.38750296875,92.3681496875Q13.28773796875,92.5560796875,13.204229968749999,92.7465896875Q13.12072296875,92.9370896875,13.05367396875,93.1297096875Q12.98662496875,93.3223196875,12.93619696875,93.5165896875Q12.88576796875,93.7108496875,12.85208176875,93.9062896875Q12.81839516875,94.1017396875,12.80153156875,94.2978896875Q12.78466796875,94.4940396875,12.78466796875,94.6904296875Q12.78466796875,94.8868196875,12.80153156875,95.0829696875Q12.81839516875,95.2791196875,12.85208176875,95.47456968750001Q12.88576796875,95.6700096875,12.93619696875,95.8642696875Q12.98662496875,96.0585396875,13.05367396875,96.2511496875Q13.12072296875,96.4437696875,13.204229968749999,96.6342696875Q13.28773796875,96.8247296875,13.38750296875,97.0127296875Q13.48726796875,97.2006296875,13.60305096875,97.3855296875Q13.71883296875,97.5704296875,13.85035796875,97.7519296875Q13.98187796875,97.9333296875,14.128817968749999,98.1108296875Q14.27575796875,98.2884296875,14.43776796875,98.4616296875Q14.59977796875,98.6348296875,14.77646796875,98.8032296875Q14.95315796875,98.9717296875,15.14409796875,99.1350296875Q15.33502796875,99.2983296875,15.53975796875,99.4560296875Q15.74448796875,99.6137296875,15.96251796875,99.7655296875Q16.18054796875,99.9174296875,16.41134796875,100.0629296875Q16.64215796875,100.2084296875,16.88516796875,100.34732968750001Q17.128187968749998,100.4861296875,17.38283796875,100.6180296875Q17.63748796875,100.7499296875,17.90315796875,100.8745296875Q18.16882796875,100.9991296875,18.44487796875,101.1161296875Q18.72092796875,101.2330296875,19.00668796875,101.3422296875Q19.29244796875,101.45132968749999,19.58722796875,101.5522296875Q19.88201796875,101.6532296875,20.18511796875,101.7458296875Q20.48820796875,101.83832968749999,20.798897968749998,101.9223296875Q21.10957796875,102.0063296875,21.427097968749997,102.0814296875Q21.74461796875,102.1566296875,22.068207968750002,102.2228296875Q22.391797968749998,102.2889296875,22.72068796875,102.3459296875Q23.04956796875,102.4029296875,23.382967968750002,102.4506296875Q23.71636796875,102.49842968749999,24.053367968750003,102.5367296875Q24.390467968750002,102.5750296875,24.73046796875,102.6038296875Q25.07036796875,102.6326296875,25.41246796875,102.6519296875Q25.75446796875,102.6711296875,26.09776796875,102.6808296875Q26.44096796875,102.6904296875,26.78466796875,102.6904296875ZM36.188067968750005,89.9018196875Q39.78466796875,91.9570496875,39.78466796875,94.6904296875Q39.78466796875,97.4238296875,36.188067968750005,99.4790296875Q32.31806796875,101.6904296875,26.78466796875,101.6904296875Q21.25124796875,101.6904296875,17.38130796875,99.4790296875Q13.78466796875,97.4238296875,13.78466796875,94.6904296875Q13.78466796875,91.9570396875,17.38130796875,89.9018196875Q21.25123796875,87.6904296875,26.78466796875,87.6904296875Q32.31806796875,87.6904296875,36.188067968750005,89.9018196875Z"
            fill-rule="evenodd" fill="var(--color-dark)" fill-opacity="1" />
        </g>
        <g>
          <path
            d="M26.78467203125,100.15856796875Q27.01958203125,100.15856796875,27.254232031249998,100.15196796875Q27.48883203125,100.14536796875,27.72263203125,100.13226796875Q27.95643203125,100.11906796875,28.188732031249998,100.09936796875Q28.42113203125,100.07966796875,28.65153203125,100.05346796875Q28.881932031250003,100.02726796875,29.10983203125,99.99466796875001Q29.33773203125,99.96206796875,29.56253203125,99.92306796875Q29.78733203125,99.88416796875,30.008432031250003,99.83886796875Q30.22963203125,99.79366796875,30.44673203125,99.74226796875Q30.66373203125,99.69096796875,30.876132031250002,99.63356796875Q31.08843203125,99.57616796875,31.29563203125,99.51286796875Q31.50283203125,99.44956796875,31.70433203125,99.38056796875Q31.90573203125,99.31156796875,32.10113203125,99.23696796875Q32.29643203125,99.16241796875,32.485132031250004,99.08244796875Q32.67383203125,99.00248796875,32.855432031250004,98.91732796875Q33.036932031250004,98.83216796875,33.21103203125,98.74202796875Q33.385132031249995,98.65187796875,33.551232031249995,98.55695796875Q33.717332031249995,98.46203796875,33.87503203125,98.36257796875Q34.032832031249995,98.26310796875,34.181832031249996,98.15934796875Q34.33093203125,98.05557796875,34.47083203125,97.94775796875Q34.610732031249995,97.83993796875001,34.74123203125,97.72832796875Q34.87183203125,97.61670796875,34.99253203125,97.50156796875Q35.113332031249996,97.38643796875,35.224032031250005,97.26804796875Q35.33483203125,97.14965796875,35.435232031249996,97.02830796875Q35.53563203125,96.90696796875,35.62553203125,96.78294796875Q35.71543203125,96.65892796874999,35.79463203125,96.53253796875Q35.87373203125,96.40614796875,35.941932031250005,96.27768796875Q36.010132031249995,96.14923796875,36.06723203125,96.01901796875Q36.12433203125,95.88880796875,36.17013203125,95.75714796875Q36.21593203125,95.62549796875,36.25043203125,95.49270796875Q36.28493203125,95.35992796875,36.307932031250004,95.22633796875Q36.33093203125,95.09274796875,36.34243203125,94.95866796875Q36.35403203125,94.82459796875,36.35403203125,94.69035796874999Q36.35403203125,94.55612796875,36.34243203125,94.42204796875001Q36.33093203125,94.28797796875,36.307932031250004,94.15438796875Q36.28493203125,94.02079796875,36.25043203125,93.88800796875Q36.21593203125,93.75522796875,36.17013203125,93.62356796875Q36.12433203125,93.49190796875,36.06723203125,93.36169796875Q36.010132031249995,93.23148796875,35.941932031250005,93.10302796875Q35.87373203125,92.97456796875,35.79463203125,92.84817796875Q35.71543203125,92.72178796875,35.62553203125,92.59777796875Q35.53563203125,92.47375796875,35.435232031249996,92.35240796875Q35.33483203125,92.23105796875,35.224032031250005,92.11266796875Q35.113332031249996,91.99428796875,34.99253203125,91.87914796875Q34.87183203125,91.76400796875,34.74123203125,91.65239796875Q34.610732031249995,91.54077796875,34.47083203125,91.43295796875Q34.33093203125,91.32513796875,34.181832031249996,91.22137796875Q34.032832031249995,91.11760796875,33.87503203125,91.01814796875Q33.717332031249995,90.91868796875,33.551232031249995,90.82376796875Q33.385132031249995,90.72884796875,33.21103203125,90.63869796875Q33.036932031250004,90.54854796875,32.855432031250004,90.46338796875Q32.67383203125,90.37822796875,32.485132031250004,90.29826796875Q32.29643203125,90.21830196875,32.10113203125,90.14372396875Q31.90573203125,90.06914696875,31.70433203125,90.00013496875Q31.50283203125,89.93112396875,31.29563203125,89.86784496875Q31.08843203125,89.80456596875,30.876132031250002,89.74717296875Q30.66373203125,89.68977896875,30.44673203125,89.63840896875Q30.22963203125,89.58703896875,30.008432031250003,89.54181596875Q29.78733203125,89.49659296875,29.56253203125,89.45762596875Q29.33773203125,89.41865996875,29.10983203125,89.38604296875Q28.881932031250003,89.35342596875,28.65153203125,89.32723796875Q28.42113203125,89.30104946875,28.188732031249998,89.28135286875Q27.95643203125,89.26165626875,27.72263203125,89.24849876875Q27.48883203125,89.23534136875,27.254232031249998,89.22875464875Q27.01958203125,89.22216796875,26.78467203125,89.22216796875Q26.54975203125,89.22216796875,26.31512203125,89.22875464875Q26.08049203125,89.23534136875,25.84671203125,89.24849876875Q25.61292203125,89.26165626875,25.380552031249998,89.28135286875Q25.148182031250002,89.30104946875,24.91778203125,89.32723796875Q24.68738203125,89.35342596875,24.45951203125,89.38604296875Q24.23163203125,89.41865996875,24.00683203125,89.45762596875Q23.78204203125,89.49659296875,23.56085203125,89.54181596875Q23.33967203125,89.58703896875,23.12264203125,89.63840896875Q22.90561203125,89.68977896875,22.69325203125,89.74717296875Q22.48089203125,89.80456596875,22.27371203125,89.86784496875Q22.066542031250002,89.93112396875,21.86504203125,90.00013496875Q21.66355203125,90.06914696875,21.46823203125,90.14372396875Q21.27290203125,90.21830196875,21.08422203125,90.29826796875Q20.89553203125,90.37822796875,20.71394203125,90.46338796875Q20.53235203125,90.54854796875,20.35829203125,90.63869796875Q20.18423203125,90.72884796875,20.01812203125,90.82376796875Q19.85201203125,90.91868796875,19.69426203125,91.01814796875Q19.53650203125,91.11760796875,19.38747203125,91.22137796875Q19.23844203125,91.32513796875,19.09850203125,91.43295796875Q18.95856203125,91.54077796875,18.82805203125,91.65239796875Q18.69754203125,91.76400796875,18.57677203125,91.87914796875Q18.45600203125,91.99428796875,18.34527203125,92.11266796875Q18.23453203125,92.23105796875,18.13409103125,92.35240796875Q18.03365203125,92.47375796875,17.94375403125,92.59777796875Q17.85385703125,92.72178796875,17.77471603125,92.84817796875Q17.69557603125,92.97456796875,17.62738403125,93.10302796875Q17.55919203125,93.23148796875,17.50211303125,93.36169796875Q17.44503403125,93.49190796875,17.39920403125,93.62356796875Q17.35337503125,93.75522796875,17.31890603125,93.88800796875Q17.28443663125,94.02079796875,17.26141103125,94.15438796875Q17.23838543125,94.28797796875,17.22685873125,94.42204796875001Q17.21533203125,94.55612796875,17.21533203125,94.69035796874999Q17.21533203125,94.82459796875,17.22685873125,94.95866796875Q17.23838543125,95.09274796875,17.26141103125,95.22633796875Q17.28443663125,95.35992796875,17.31890603125,95.49270796875Q17.35337503125,95.62549796875,17.39920403125,95.75714796875Q17.44503403125,95.88880796875,17.50211303125,96.01901796875Q17.55919203125,96.14923796875,17.62738403125,96.27768796875Q17.69557603125,96.40614796875,17.77471603125,96.53253796875Q17.85385703125,96.65892796874999,17.94375403125,96.78294796875Q18.03365203125,96.90696796875,18.13409103125,97.02830796875Q18.23453203125,97.14965796875,18.34527203125,97.26804796875Q18.45600203125,97.38642796875,18.57677203125,97.50156796875Q18.69754203125,97.61670796875,18.82805203125,97.72832796875Q18.95856203125,97.83993796875001,19.09850203125,97.94775796875Q19.23844203125,98.05557796875,19.38747203125,98.15934796875Q19.53650203125,98.26310796875,19.69426203125,98.36257796875Q19.85201203125,98.46203796875,20.01812203125,98.55695796875Q20.18423203125,98.65187796875,20.35829203125,98.74202796875Q20.53235203125,98.83216796875,20.71394203125,98.91732796875Q20.89553203125,99.00248796875,21.08422203125,99.08244796875Q21.27290203125,99.16241796875,21.46823203125,99.23696796875Q21.66355203125,99.31156796875,21.86504203125,99.38056796875Q22.066542031250002,99.44956796875,22.27371203125,99.51286796875Q22.48089203125,99.57616796875,22.69325203125,99.63356796875Q22.90561203125,99.69096796875,23.12264203125,99.74226796875Q23.33967203125,99.79366796875,23.56085203125,99.83886796875Q23.78204203125,99.88416796875,24.00683203125,99.92306796875Q24.23163203125,99.96206796875,24.45951203125,99.99466796875001Q24.68738203125,100.02726796875,24.91778203125,100.05346796875Q25.148182031250002,100.07966796875,25.380552031249998,100.09936796875Q25.61292203125,100.11906796875,25.84671203125,100.13226796875Q26.08049203125,100.14536796875,26.31512203125,100.15196796875Q26.54975203125,100.15856796875,26.78467203125,100.15856796875ZM33.05503203125,91.69200796875Q35.35403203125,93.00568796875,35.35403203125,94.69035796874999Q35.35403203125,96.37503796875001,33.05503203125,97.68871796875Q30.48283203125,99.15854796875,26.78467203125,99.15854796875Q23.08648203125,99.15855796875,20.51426203125,97.68871796875Q18.21533103125,96.37503796875001,18.21533203125,94.69035796874999Q18.21533203125,93.00568796875,20.51426203125,91.69200796875Q23.08648203125,90.22216796875,26.78467203125,90.22216796875Q30.48283203125,90.22216796875,33.05503203125,91.69200796875Z"
            fill-rule="evenodd" fill="var(--color-dark)" fill-opacity="1" />
        </g>
      </g>
      <g filter="url(#${lineName}0_223_09593)">
        <g>
          <path
            d="M26.78466796875,102.6904296875Q27.12836796875,102.6904296875,27.47156796875,102.6808296875Q27.81486796875,102.6711296875,28.15686796875,102.6519296875Q28.49896796875,102.6326296875,28.83886796875,102.6038296875Q29.17886796875,102.5750296875,29.51596796875,102.5367296875Q29.85296796875,102.49842968749999,30.18636796875,102.4506296875Q30.51976796875,102.4029296875,30.84866796875,102.3459296875Q31.17756796875,102.2889296875,31.50116796875,102.2228296875Q31.82466796875,102.1566296875,32.142267968750005,102.0814296875Q32.45976796875,102.0063296875,32.77046796875,101.9223296875Q33.08116796875,101.83832968749999,33.384267968749995,101.7458296875Q33.687367968749996,101.6532296875,33.98206796875,101.5522296875Q34.27686796875,101.45132968749999,34.56266796875,101.3422296875Q34.84836796875,101.2330296875,35.12446796875,101.1161296875Q35.40046796875,100.9991296875,35.66616796875,100.8745296875Q35.93186796875,100.7499296875,36.18646796875,100.6180296875Q36.44116796875,100.4861296875,36.68416796875,100.34732968750001Q36.92716796875,100.2084296875,37.15796796875,100.0629296875Q37.38876796875,99.9174296875,37.60676796875,99.7655296875Q37.82486796875,99.6137296875,38.02956796875,99.4560296875Q38.234267968750004,99.2983296875,38.42526796875,99.1350296875Q38.61616796875,98.9717296875,38.79286796875,98.8032296875Q38.96956796875,98.6348296875,39.131567968750005,98.4616296875Q39.29356796875,98.2884296875,39.44046796875,98.1108296875Q39.587467968750005,97.9333296875,39.71896796875,97.7519296875Q39.850467968749996,97.5704296875,39.96626796875,97.3855296875Q40.082067968749996,97.2006296875,40.18186796875,97.0127296875Q40.28156796875,96.8247296875,40.36506796875,96.6342696875Q40.44856796875,96.4437696875,40.51566796875,96.2511496875Q40.58266796875,96.0585396875,40.63316796875,95.8642696875Q40.68356796875,95.6700096875,40.71726796875,95.47456968750001Q40.750967968750004,95.2791196875,40.76776796875,95.0829696875Q40.78466796875,94.8868196875,40.78466796875,94.6904296875Q40.78466796875,94.4940396875,40.76776796875,94.2978896875Q40.750967968750004,94.1017396875,40.71726796875,93.9062896875Q40.68356796875,93.7108496875,40.63316796875,93.5165896875Q40.58266796875,93.3223196875,40.51566796875,93.1297096875Q40.44856796875,92.9370896875,40.36506796875,92.7465896875Q40.28156796875,92.5560796875,40.18186796875,92.3681496875Q40.082067968749996,92.1802196875,39.96626796875,91.9953096875Q39.850467968749996,91.8103996875,39.71896796875,91.6289596875Q39.587467968750005,91.4475196875,39.44046796875,91.2699896875Q39.29356796875,91.0924596875,39.131567968750005,90.9192596875Q38.96956796875,90.7460596875,38.79286796875,90.5776096875Q38.61616796875,90.4091596875,38.42526796875,90.2458696875Q38.234267968750004,90.0825796875,38.02956796875,89.9248396875Q37.82486796875,89.7670896875,37.60676796875,89.6152796875Q37.38876796875,89.4634696875,37.15796796875,89.31795968750001Q36.92716796875,89.1724396875,36.68416796875,89.0335796875Q36.44116796875,88.8947096875,36.18646796875,88.7628196875Q35.93186796875,88.6309296875,35.66616796875,88.5063496875Q35.40046796875,88.3817596875,35.12446796875,88.2647696875Q34.84836796875,88.1477796875,34.56266796875,88.0386696875Q34.27686796875,87.9295696875,33.98206796875,87.8285996875Q33.687367968749996,87.7276396875,33.384267968749995,87.6350596875Q33.08116796875,87.5424826875,32.77046796875,87.4585156875Q32.45976796875,87.3745476875,32.142267968750005,87.2993936875Q31.82466796875,87.2242386875,31.50116796875,87.1580766875Q31.17756796875,87.0919156875,30.84866796875,87.0349066875Q30.51976796875,86.9778986875,30.18636796875,86.9301796875Q29.85296796875,86.8824606875,29.51596796875,86.8441476875Q29.17886796875,86.8058336875,28.83886796875,86.7770175875Q28.49896796875,86.7482013875,28.15686796875,86.7289518875Q27.81486796875,86.7097023875,27.47156796875,86.7000660375Q27.12836796875,86.6904296875,26.78466796875,86.6904296875Q26.44096796875,86.6904296875,26.09776796875,86.7000660375Q25.75446796875,86.7097023875,25.41246796875,86.7289518875Q25.07036796875,86.7482013875,24.73046796875,86.7770175875Q24.390467968750002,86.8058336875,24.053367968750003,86.8441476875Q23.71636796875,86.8824606875,23.382967968750002,86.9301796875Q23.04956796875,86.9778986875,22.72068796875,87.0349066875Q22.391797968749998,87.0919156875,22.068207968750002,87.1580766875Q21.74461796875,87.2242386875,21.427097968749997,87.2993936875Q21.10957796875,87.3745476875,20.798897968749998,87.4585156875Q20.48820796875,87.5424826875,20.18511796875,87.6350596875Q19.88201796875,87.7276396875,19.58722796875,87.8285996875Q19.29244796875,87.9295696875,19.00668796875,88.0386696875Q18.72092796875,88.1477796875,18.44487796875,88.2647696875Q18.16882796875,88.3817596875,17.90315796875,88.5063496875Q17.63749796875,88.6309296875,17.38283796875,88.7628196875Q17.128187968749998,88.8947096875,16.88516796875,89.0335796875Q16.64215796875,89.1724396875,16.41134796875,89.31795968750001Q16.18054796875,89.4634696875,15.96251796875,89.6152796875Q15.74448796875,89.7670896875,15.53975796875,89.9248396875Q15.33502796875,90.0825796875,15.14409796875,90.2458696875Q14.95315796875,90.4091596875,14.77646796875,90.5776096875Q14.59977796875,90.7460596875,14.43776796875,90.9192596875Q14.27575796875,91.0924596875,14.128817968749999,91.2699896875Q13.98187796875,91.4475196875,13.85035796875,91.6289596875Q13.71883296875,91.8103996875,13.60305096875,91.9953096875Q13.48726796875,92.1802196875,13.38750296875,92.3681496875Q13.28773796875,92.5560796875,13.204229968749999,92.7465896875Q13.12072296875,92.9370896875,13.05367396875,93.1297096875Q12.98662496875,93.3223196875,12.93619696875,93.5165896875Q12.88576796875,93.7108496875,12.85208176875,93.9062896875Q12.81839516875,94.1017396875,12.80153156875,94.2978896875Q12.78466796875,94.4940396875,12.78466796875,94.6904296875Q12.78466796875,94.8868196875,12.80153156875,95.0829696875Q12.81839516875,95.2791196875,12.85208176875,95.47456968750001Q12.88576796875,95.6700096875,12.93619696875,95.8642696875Q12.98662496875,96.0585396875,13.05367396875,96.2511496875Q13.12072296875,96.4437696875,13.204229968749999,96.6342696875Q13.28773796875,96.8247296875,13.38750296875,97.0127296875Q13.48726796875,97.2006296875,13.60305096875,97.3855296875Q13.71883296875,97.5704296875,13.85035796875,97.7519296875Q13.98187796875,97.9333296875,14.128817968749999,98.1108296875Q14.27575796875,98.2884296875,14.43776796875,98.4616296875Q14.59977796875,98.6348296875,14.77646796875,98.8032296875Q14.95315796875,98.9717296875,15.14409796875,99.1350296875Q15.33502796875,99.2983296875,15.53975796875,99.4560296875Q15.74448796875,99.6137296875,15.96251796875,99.7655296875Q16.18054796875,99.9174296875,16.41134796875,100.0629296875Q16.64215796875,100.2084296875,16.88516796875,100.34732968750001Q17.128187968749998,100.4861296875,17.38283796875,100.6180296875Q17.63748796875,100.7499296875,17.90315796875,100.8745296875Q18.16882796875,100.9991296875,18.44487796875,101.1161296875Q18.72092796875,101.2330296875,19.00668796875,101.3422296875Q19.29244796875,101.45132968749999,19.58722796875,101.5522296875Q19.88201796875,101.6532296875,20.18511796875,101.7458296875Q20.48820796875,101.83832968749999,20.798897968749998,101.9223296875Q21.10957796875,102.0063296875,21.427097968749997,102.0814296875Q21.74461796875,102.1566296875,22.068207968750002,102.2228296875Q22.391797968749998,102.2889296875,22.72068796875,102.3459296875Q23.04956796875,102.4029296875,23.382967968750002,102.4506296875Q23.71636796875,102.49842968749999,24.053367968750003,102.5367296875Q24.390467968750002,102.5750296875,24.73046796875,102.6038296875Q25.07036796875,102.6326296875,25.41246796875,102.6519296875Q25.75446796875,102.6711296875,26.09776796875,102.6808296875Q26.44096796875,102.6904296875,26.78466796875,102.6904296875ZM36.03916796875,90.1622896875Q39.48466796875,92.1311396875,39.48466796875,94.6904296875Q39.48466796875,97.24972968750001,36.03916796875,99.2185296875Q32.23846796875,101.3904296875,26.78466796875,101.3904296875Q21.330907968749997,101.3904296875,17.53015796875,99.2185296875Q14.08466796875,97.24972968750001,14.08466796875,94.6904296875Q14.08466796875,92.1311396875,17.53015796875,90.1622896875Q21.330907968749997,87.9904296875,26.78466796875,87.9904296875Q32.23846796875,87.9904296875,36.03916796875,90.1622896875Z"
            fill-rule="evenodd" fill="var(--color)" fill-opacity="1" />
        </g>
        <g>
          <path
            d="M26.78467203125,100.15856796875Q27.01958203125,100.15856796875,27.254232031249998,100.15196796875Q27.48883203125,100.14536796875,27.72263203125,100.13226796875Q27.95643203125,100.11906796875,28.188732031249998,100.09936796875Q28.42113203125,100.07966796875,28.65153203125,100.05346796875Q28.881932031250003,100.02726796875,29.10983203125,99.99466796875001Q29.33773203125,99.96206796875,29.56253203125,99.92306796875Q29.78733203125,99.88416796875,30.008432031250003,99.83886796875Q30.22963203125,99.79366796875,30.44673203125,99.74226796875Q30.66373203125,99.69096796875,30.876132031250002,99.63356796875Q31.08843203125,99.57616796875,31.29563203125,99.51286796875Q31.50283203125,99.44956796875,31.70433203125,99.38056796875Q31.90573203125,99.31156796875,32.10113203125,99.23696796875Q32.29643203125,99.16241796875,32.485132031250004,99.08244796875Q32.67383203125,99.00248796875,32.855432031250004,98.91732796875Q33.036932031250004,98.83216796875,33.21103203125,98.74202796875Q33.385132031249995,98.65187796875,33.551232031249995,98.55695796875Q33.717332031249995,98.46203796875,33.87503203125,98.36257796875Q34.032832031249995,98.26310796875,34.181832031249996,98.15934796875Q34.33093203125,98.05557796875,34.47083203125,97.94775796875Q34.610732031249995,97.83993796875001,34.74123203125,97.72832796875Q34.87183203125,97.61670796875,34.99253203125,97.50156796875Q35.113332031249996,97.38643796875,35.224032031250005,97.26804796875Q35.33483203125,97.14965796875,35.435232031249996,97.02830796875Q35.53563203125,96.90696796875,35.62553203125,96.78294796875Q35.71543203125,96.65892796874999,35.79463203125,96.53253796875Q35.87373203125,96.40614796875,35.941932031250005,96.27768796875Q36.010132031249995,96.14923796875,36.06723203125,96.01901796875Q36.12433203125,95.88880796875,36.17013203125,95.75714796875Q36.21593203125,95.62549796875,36.25043203125,95.49270796875Q36.28493203125,95.35992796875,36.307932031250004,95.22633796875Q36.33093203125,95.09274796875,36.34243203125,94.95866796875Q36.35403203125,94.82459796875,36.35403203125,94.69035796874999Q36.35403203125,94.55612796875,36.34243203125,94.42204796875001Q36.33093203125,94.28797796875,36.307932031250004,94.15438796875Q36.28493203125,94.02079796875,36.25043203125,93.88800796875Q36.21593203125,93.75522796875,36.17013203125,93.62356796875Q36.12433203125,93.49190796875,36.06723203125,93.36169796875Q36.010132031249995,93.23148796875,35.941932031250005,93.10302796875Q35.87373203125,92.97456796875,35.79463203125,92.84817796875Q35.71543203125,92.72178796875,35.62553203125,92.59777796875Q35.53563203125,92.47375796875,35.435232031249996,92.35240796875Q35.33483203125,92.23105796875,35.224032031250005,92.11266796875Q35.113332031249996,91.99428796875,34.99253203125,91.87914796875Q34.87183203125,91.76400796875,34.74123203125,91.65239796875Q34.610732031249995,91.54077796875,34.47083203125,91.43295796875Q34.33093203125,91.32513796875,34.181832031249996,91.22137796875Q34.032832031249995,91.11760796875,33.87503203125,91.01814796875Q33.717332031249995,90.91868796875,33.551232031249995,90.82376796875Q33.385132031249995,90.72884796875,33.21103203125,90.63869796875Q33.036932031250004,90.54854796875,32.855432031250004,90.46338796875Q32.67383203125,90.37822796875,32.485132031250004,90.29826796875Q32.29643203125,90.21830196875,32.10113203125,90.14372396875Q31.90573203125,90.06914696875,31.70433203125,90.00013496875Q31.50283203125,89.93112396875,31.29563203125,89.86784496875Q31.08843203125,89.80456596875,30.876132031250002,89.74717296875Q30.66373203125,89.68977896875,30.44673203125,89.63840896875Q30.22963203125,89.58703896875,30.008432031250003,89.54181596875Q29.78733203125,89.49659296875,29.56253203125,89.45762596875Q29.33773203125,89.41865996875,29.10983203125,89.38604296875Q28.881932031250003,89.35342596875,28.65153203125,89.32723796875Q28.42113203125,89.30104946875,28.188732031249998,89.28135286875Q27.95643203125,89.26165626875,27.72263203125,89.24849876875Q27.48883203125,89.23534136875,27.254232031249998,89.22875464875Q27.01958203125,89.22216796875,26.78467203125,89.22216796875Q26.54975203125,89.22216796875,26.31512203125,89.22875464875Q26.08049203125,89.23534136875,25.84671203125,89.24849876875Q25.61292203125,89.26165626875,25.380552031249998,89.28135286875Q25.148182031250002,89.30104946875,24.91778203125,89.32723796875Q24.68738203125,89.35342596875,24.45951203125,89.38604296875Q24.23163203125,89.41865996875,24.00683203125,89.45762596875Q23.78204203125,89.49659296875,23.56085203125,89.54181596875Q23.33967203125,89.58703896875,23.12264203125,89.63840896875Q22.90561203125,89.68977896875,22.69325203125,89.74717296875Q22.48089203125,89.80456596875,22.27371203125,89.86784496875Q22.066542031250002,89.93112396875,21.86504203125,90.00013496875Q21.66355203125,90.06914696875,21.46823203125,90.14372396875Q21.27290203125,90.21830196875,21.08422203125,90.29826796875Q20.89553203125,90.37822796875,20.71394203125,90.46338796875Q20.53235203125,90.54854796875,20.35829203125,90.63869796875Q20.18423203125,90.72884796875,20.01812203125,90.82376796875Q19.85201203125,90.91868796875,19.69426203125,91.01814796875Q19.53650203125,91.11760796875,19.38747203125,91.22137796875Q19.23844203125,91.32513796875,19.09850203125,91.43295796875Q18.95856203125,91.54077796875,18.82805203125,91.65239796875Q18.69754203125,91.76400796875,18.57677203125,91.87914796875Q18.45600203125,91.99428796875,18.34527203125,92.11266796875Q18.23453203125,92.23105796875,18.13409103125,92.35240796875Q18.03365203125,92.47375796875,17.94375403125,92.59777796875Q17.85385703125,92.72178796875,17.77471603125,92.84817796875Q17.69557603125,92.97456796875,17.62738403125,93.10302796875Q17.55919203125,93.23148796875,17.50211303125,93.36169796875Q17.44503403125,93.49190796875,17.39920403125,93.62356796875Q17.35337503125,93.75522796875,17.31890603125,93.88800796875Q17.28443663125,94.02079796875,17.26141103125,94.15438796875Q17.23838543125,94.28797796875,17.22685873125,94.42204796875001Q17.21533203125,94.55612796875,17.21533203125,94.69035796874999Q17.21533203125,94.82459796875,17.22685873125,94.95866796875Q17.23838543125,95.09274796875,17.26141103125,95.22633796875Q17.28443663125,95.35992796875,17.31890603125,95.49270796875Q17.35337503125,95.62549796875,17.39920403125,95.75714796875Q17.44503403125,95.88880796875,17.50211303125,96.01901796875Q17.55919203125,96.14923796875,17.62738403125,96.27768796875Q17.69557603125,96.40614796875,17.77471603125,96.53253796875Q17.85385703125,96.65892796874999,17.94375403125,96.78294796875Q18.03365203125,96.90696796875,18.13409103125,97.02830796875Q18.23453203125,97.14965796875,18.34527203125,97.26804796875Q18.45600203125,97.38642796875,18.57677203125,97.50156796875Q18.69754203125,97.61670796875,18.82805203125,97.72832796875Q18.95856203125,97.83993796875001,19.09850203125,97.94775796875Q19.23844203125,98.05557796875,19.38747203125,98.15934796875Q19.53650203125,98.26310796875,19.69426203125,98.36257796875Q19.85201203125,98.46203796875,20.01812203125,98.55695796875Q20.18423203125,98.65187796875,20.35829203125,98.74202796875Q20.53235203125,98.83216796875,20.71394203125,98.91732796875Q20.89553203125,99.00248796875,21.08422203125,99.08244796875Q21.27290203125,99.16241796875,21.46823203125,99.23696796875Q21.66355203125,99.31156796875,21.86504203125,99.38056796875Q22.066542031250002,99.44956796875,22.27371203125,99.51286796875Q22.48089203125,99.57616796875,22.69325203125,99.63356796875Q22.90561203125,99.69096796875,23.12264203125,99.74226796875Q23.33967203125,99.79366796875,23.56085203125,99.83886796875Q23.78204203125,99.88416796875,24.00683203125,99.92306796875Q24.23163203125,99.96206796875,24.45951203125,99.99466796875001Q24.68738203125,100.02726796875,24.91778203125,100.05346796875Q25.148182031250002,100.07966796875,25.380552031249998,100.09936796875Q25.61292203125,100.11906796875,25.84671203125,100.13226796875Q26.08049203125,100.14536796875,26.31512203125,100.15196796875Q26.54975203125,100.15856796875,26.78467203125,100.15856796875ZM32.90623203125,91.95247796875Q35.05403203125,93.17977796875,35.05403203125,94.69035796874999Q35.05403203125,96.20093796875,32.90623203125,97.42823796875Q30.40323203125,98.85854796875,26.78467203125,98.85854796875Q23.16615203125,98.85854796875,20.66310203125,97.42823796875Q18.51533203125,96.20093796875,18.51533203125,94.69035796874999Q18.51533203125,93.17977796875,20.66310203125,91.95247796875Q23.16615203125,90.52216796875,26.78467203125,90.52216796875Q30.40313203125,90.52216796875,32.90623203125,91.95247796875Z"
            fill-rule="evenodd" fill="var(--color)" fill-opacity="1" />
        </g>
      </g>
      <g filter="url(#${lineName}1_223_09596)">
        <g>
          <path
            d="M26.78466796875,102.6904296875Q27.12836796875,102.6904296875,27.47156796875,102.6808296875Q27.81486796875,102.6711296875,28.15686796875,102.6519296875Q28.49896796875,102.6326296875,28.83886796875,102.6038296875Q29.17886796875,102.5750296875,29.51596796875,102.5367296875Q29.85296796875,102.49842968749999,30.18636796875,102.4506296875Q30.51976796875,102.4029296875,30.84866796875,102.3459296875Q31.17756796875,102.2889296875,31.50116796875,102.2228296875Q31.82466796875,102.1566296875,32.142267968750005,102.0814296875Q32.45976796875,102.0063296875,32.77046796875,101.9223296875Q33.08116796875,101.83832968749999,33.384267968749995,101.7458296875Q33.687367968749996,101.6532296875,33.98206796875,101.5522296875Q34.27686796875,101.45132968749999,34.56266796875,101.3422296875Q34.84836796875,101.2330296875,35.12446796875,101.1161296875Q35.40046796875,100.9991296875,35.66616796875,100.8745296875Q35.93186796875,100.7499296875,36.18646796875,100.6180296875Q36.44116796875,100.4861296875,36.68416796875,100.34732968750001Q36.92716796875,100.2084296875,37.15796796875,100.0629296875Q37.38876796875,99.9174296875,37.60676796875,99.7655296875Q37.82486796875,99.6137296875,38.02956796875,99.4560296875Q38.234267968750004,99.2983296875,38.42526796875,99.1350296875Q38.61616796875,98.9717296875,38.79286796875,98.8032296875Q38.96956796875,98.6348296875,39.131567968750005,98.4616296875Q39.29356796875,98.2884296875,39.44046796875,98.1108296875Q39.587467968750005,97.9333296875,39.71896796875,97.7519296875Q39.850467968749996,97.5704296875,39.96626796875,97.3855296875Q40.082067968749996,97.2006296875,40.18186796875,97.0127296875Q40.28156796875,96.8247296875,40.36506796875,96.6342696875Q40.44856796875,96.4437696875,40.51566796875,96.2511496875Q40.58266796875,96.0585396875,40.63316796875,95.8642696875Q40.68356796875,95.6700096875,40.71726796875,95.47456968750001Q40.750967968750004,95.2791196875,40.76776796875,95.0829696875Q40.78466796875,94.8868196875,40.78466796875,94.6904296875Q40.78466796875,94.4940396875,40.76776796875,94.2978896875Q40.750967968750004,94.1017396875,40.71726796875,93.9062896875Q40.68356796875,93.7108496875,40.63316796875,93.5165896875Q40.58266796875,93.3223196875,40.51566796875,93.1297096875Q40.44856796875,92.9370896875,40.36506796875,92.7465896875Q40.28156796875,92.5560796875,40.18186796875,92.3681496875Q40.082067968749996,92.1802196875,39.96626796875,91.9953096875Q39.850467968749996,91.8103996875,39.71896796875,91.6289596875Q39.587467968750005,91.4475196875,39.44046796875,91.2699896875Q39.29356796875,91.0924596875,39.131567968750005,90.9192596875Q38.96956796875,90.7460596875,38.79286796875,90.5776096875Q38.61616796875,90.4091596875,38.42526796875,90.2458696875Q38.234267968750004,90.0825796875,38.02956796875,89.9248396875Q37.82486796875,89.7670896875,37.60676796875,89.6152796875Q37.38876796875,89.4634696875,37.15796796875,89.31795968750001Q36.92716796875,89.1724396875,36.68416796875,89.0335796875Q36.44116796875,88.8947096875,36.18646796875,88.7628196875Q35.93186796875,88.6309296875,35.66616796875,88.5063496875Q35.40046796875,88.3817596875,35.12446796875,88.2647696875Q34.84836796875,88.1477796875,34.56266796875,88.0386696875Q34.27686796875,87.9295696875,33.98206796875,87.8285996875Q33.687367968749996,87.7276396875,33.384267968749995,87.6350596875Q33.08116796875,87.5424826875,32.77046796875,87.4585156875Q32.45976796875,87.3745476875,32.142267968750005,87.2993936875Q31.82466796875,87.2242386875,31.50116796875,87.1580766875Q31.17756796875,87.0919156875,30.84866796875,87.0349066875Q30.51976796875,86.9778986875,30.18636796875,86.9301796875Q29.85296796875,86.8824606875,29.51596796875,86.8441476875Q29.17886796875,86.8058336875,28.83886796875,86.7770175875Q28.49896796875,86.7482013875,28.15686796875,86.7289518875Q27.81486796875,86.7097023875,27.47156796875,86.7000660375Q27.12836796875,86.6904296875,26.78466796875,86.6904296875Q26.44096796875,86.6904296875,26.09776796875,86.7000660375Q25.75446796875,86.7097023875,25.41246796875,86.7289518875Q25.07036796875,86.7482013875,24.73046796875,86.7770175875Q24.390467968750002,86.8058336875,24.053367968750003,86.8441476875Q23.71636796875,86.8824606875,23.382967968750002,86.9301796875Q23.04956796875,86.9778986875,22.72068796875,87.0349066875Q22.391797968749998,87.0919156875,22.068207968750002,87.1580766875Q21.74461796875,87.2242386875,21.427097968749997,87.2993936875Q21.10957796875,87.3745476875,20.798897968749998,87.4585156875Q20.48820796875,87.5424826875,20.18511796875,87.6350596875Q19.88201796875,87.7276396875,19.58722796875,87.8285996875Q19.29244796875,87.9295696875,19.00668796875,88.0386696875Q18.72092796875,88.1477796875,18.44487796875,88.2647696875Q18.16882796875,88.3817596875,17.90315796875,88.5063496875Q17.63749796875,88.6309296875,17.38283796875,88.7628196875Q17.128187968749998,88.8947096875,16.88516796875,89.0335796875Q16.64215796875,89.1724396875,16.41134796875,89.31795968750001Q16.18054796875,89.4634696875,15.96251796875,89.6152796875Q15.74448796875,89.7670896875,15.53975796875,89.9248396875Q15.33502796875,90.0825796875,15.14409796875,90.2458696875Q14.95315796875,90.4091596875,14.77646796875,90.5776096875Q14.59977796875,90.7460596875,14.43776796875,90.9192596875Q14.27575796875,91.0924596875,14.128817968749999,91.2699896875Q13.98187796875,91.4475196875,13.85035796875,91.6289596875Q13.71883296875,91.8103996875,13.60305096875,91.9953096875Q13.48726796875,92.1802196875,13.38750296875,92.3681496875Q13.28773796875,92.5560796875,13.204229968749999,92.7465896875Q13.12072296875,92.9370896875,13.05367396875,93.1297096875Q12.98662496875,93.3223196875,12.93619696875,93.5165896875Q12.88576796875,93.7108496875,12.85208176875,93.9062896875Q12.81839516875,94.1017396875,12.80153156875,94.2978896875Q12.78466796875,94.4940396875,12.78466796875,94.6904296875Q12.78466796875,94.8868196875,12.80153156875,95.0829696875Q12.81839516875,95.2791196875,12.85208176875,95.47456968750001Q12.88576796875,95.6700096875,12.93619696875,95.8642696875Q12.98662496875,96.0585396875,13.05367396875,96.2511496875Q13.12072296875,96.4437696875,13.204229968749999,96.6342696875Q13.28773796875,96.8247296875,13.38750296875,97.0127296875Q13.48726796875,97.2006296875,13.60305096875,97.3855296875Q13.71883296875,97.5704296875,13.85035796875,97.7519296875Q13.98187796875,97.9333296875,14.128817968749999,98.1108296875Q14.27575796875,98.2884296875,14.43776796875,98.4616296875Q14.59977796875,98.6348296875,14.77646796875,98.8032296875Q14.95315796875,98.9717296875,15.14409796875,99.1350296875Q15.33502796875,99.2983296875,15.53975796875,99.4560296875Q15.74448796875,99.6137296875,15.96251796875,99.7655296875Q16.18054796875,99.9174296875,16.41134796875,100.0629296875Q16.64215796875,100.2084296875,16.88516796875,100.34732968750001Q17.128187968749998,100.4861296875,17.38283796875,100.6180296875Q17.63748796875,100.7499296875,17.90315796875,100.8745296875Q18.16882796875,100.9991296875,18.44487796875,101.1161296875Q18.72092796875,101.2330296875,19.00668796875,101.3422296875Q19.29244796875,101.45132968749999,19.58722796875,101.5522296875Q19.88201796875,101.6532296875,20.18511796875,101.7458296875Q20.48820796875,101.83832968749999,20.798897968749998,101.9223296875Q21.10957796875,102.0063296875,21.427097968749997,102.0814296875Q21.74461796875,102.1566296875,22.068207968750002,102.2228296875Q22.391797968749998,102.2889296875,22.72068796875,102.3459296875Q23.04956796875,102.4029296875,23.382967968750002,102.4506296875Q23.71636796875,102.49842968749999,24.053367968750003,102.5367296875Q24.390467968750002,102.5750296875,24.73046796875,102.6038296875Q25.07036796875,102.6326296875,25.41246796875,102.6519296875Q25.75446796875,102.6711296875,26.09776796875,102.6808296875Q26.44096796875,102.6904296875,26.78466796875,102.6904296875ZM36.03916796875,90.1622896875Q39.48466796875,92.1311396875,39.48466796875,94.6904296875Q39.48466796875,97.24972968750001,36.03916796875,99.2185296875Q32.23846796875,101.3904296875,26.78466796875,101.3904296875Q21.330907968749997,101.3904296875,17.53015796875,99.2185296875Q14.08466796875,97.24972968750001,14.08466796875,94.6904296875Q14.08466796875,92.1311396875,17.53015796875,90.1622896875Q21.330907968749997,87.9904296875,26.78466796875,87.9904296875Q32.23846796875,87.9904296875,36.03916796875,90.1622896875Z"
            fill-rule="evenodd" fill="var(--color)" fill-opacity="1" />
        </g>
        <g>
          <path
            d="M26.78467203125,100.15856796875Q27.01958203125,100.15856796875,27.254232031249998,100.15196796875Q27.48883203125,100.14536796875,27.72263203125,100.13226796875Q27.95643203125,100.11906796875,28.188732031249998,100.09936796875Q28.42113203125,100.07966796875,28.65153203125,100.05346796875Q28.881932031250003,100.02726796875,29.10983203125,99.99466796875001Q29.33773203125,99.96206796875,29.56253203125,99.92306796875Q29.78733203125,99.88416796875,30.008432031250003,99.83886796875Q30.22963203125,99.79366796875,30.44673203125,99.74226796875Q30.66373203125,99.69096796875,30.876132031250002,99.63356796875Q31.08843203125,99.57616796875,31.29563203125,99.51286796875Q31.50283203125,99.44956796875,31.70433203125,99.38056796875Q31.90573203125,99.31156796875,32.10113203125,99.23696796875Q32.29643203125,99.16241796875,32.485132031250004,99.08244796875Q32.67383203125,99.00248796875,32.855432031250004,98.91732796875Q33.036932031250004,98.83216796875,33.21103203125,98.74202796875Q33.385132031249995,98.65187796875,33.551232031249995,98.55695796875Q33.717332031249995,98.46203796875,33.87503203125,98.36257796875Q34.032832031249995,98.26310796875,34.181832031249996,98.15934796875Q34.33093203125,98.05557796875,34.47083203125,97.94775796875Q34.610732031249995,97.83993796875001,34.74123203125,97.72832796875Q34.87183203125,97.61670796875,34.99253203125,97.50156796875Q35.113332031249996,97.38643796875,35.224032031250005,97.26804796875Q35.33483203125,97.14965796875,35.435232031249996,97.02830796875Q35.53563203125,96.90696796875,35.62553203125,96.78294796875Q35.71543203125,96.65892796874999,35.79463203125,96.53253796875Q35.87373203125,96.40614796875,35.941932031250005,96.27768796875Q36.010132031249995,96.14923796875,36.06723203125,96.01901796875Q36.12433203125,95.88880796875,36.17013203125,95.75714796875Q36.21593203125,95.62549796875,36.25043203125,95.49270796875Q36.28493203125,95.35992796875,36.307932031250004,95.22633796875Q36.33093203125,95.09274796875,36.34243203125,94.95866796875Q36.35403203125,94.82459796875,36.35403203125,94.69035796874999Q36.35403203125,94.55612796875,36.34243203125,94.42204796875001Q36.33093203125,94.28797796875,36.307932031250004,94.15438796875Q36.28493203125,94.02079796875,36.25043203125,93.88800796875Q36.21593203125,93.75522796875,36.17013203125,93.62356796875Q36.12433203125,93.49190796875,36.06723203125,93.36169796875Q36.010132031249995,93.23148796875,35.941932031250005,93.10302796875Q35.87373203125,92.97456796875,35.79463203125,92.84817796875Q35.71543203125,92.72178796875,35.62553203125,92.59777796875Q35.53563203125,92.47375796875,35.435232031249996,92.35240796875Q35.33483203125,92.23105796875,35.224032031250005,92.11266796875Q35.113332031249996,91.99428796875,34.99253203125,91.87914796875Q34.87183203125,91.76400796875,34.74123203125,91.65239796875Q34.610732031249995,91.54077796875,34.47083203125,91.43295796875Q34.33093203125,91.32513796875,34.181832031249996,91.22137796875Q34.032832031249995,91.11760796875,33.87503203125,91.01814796875Q33.717332031249995,90.91868796875,33.551232031249995,90.82376796875Q33.385132031249995,90.72884796875,33.21103203125,90.63869796875Q33.036932031250004,90.54854796875,32.855432031250004,90.46338796875Q32.67383203125,90.37822796875,32.485132031250004,90.29826796875Q32.29643203125,90.21830196875,32.10113203125,90.14372396875Q31.90573203125,90.06914696875,31.70433203125,90.00013496875Q31.50283203125,89.93112396875,31.29563203125,89.86784496875Q31.08843203125,89.80456596875,30.876132031250002,89.74717296875Q30.66373203125,89.68977896875,30.44673203125,89.63840896875Q30.22963203125,89.58703896875,30.008432031250003,89.54181596875Q29.78733203125,89.49659296875,29.56253203125,89.45762596875Q29.33773203125,89.41865996875,29.10983203125,89.38604296875Q28.881932031250003,89.35342596875,28.65153203125,89.32723796875Q28.42113203125,89.30104946875,28.188732031249998,89.28135286875Q27.95643203125,89.26165626875,27.72263203125,89.24849876875Q27.48883203125,89.23534136875,27.254232031249998,89.22875464875Q27.01958203125,89.22216796875,26.78467203125,89.22216796875Q26.54975203125,89.22216796875,26.31512203125,89.22875464875Q26.08049203125,89.23534136875,25.84671203125,89.24849876875Q25.61292203125,89.26165626875,25.380552031249998,89.28135286875Q25.148182031250002,89.30104946875,24.91778203125,89.32723796875Q24.68738203125,89.35342596875,24.45951203125,89.38604296875Q24.23163203125,89.41865996875,24.00683203125,89.45762596875Q23.78204203125,89.49659296875,23.56085203125,89.54181596875Q23.33967203125,89.58703896875,23.12264203125,89.63840896875Q22.90561203125,89.68977896875,22.69325203125,89.74717296875Q22.48089203125,89.80456596875,22.27371203125,89.86784496875Q22.066542031250002,89.93112396875,21.86504203125,90.00013496875Q21.66355203125,90.06914696875,21.46823203125,90.14372396875Q21.27290203125,90.21830196875,21.08422203125,90.29826796875Q20.89553203125,90.37822796875,20.71394203125,90.46338796875Q20.53235203125,90.54854796875,20.35829203125,90.63869796875Q20.18423203125,90.72884796875,20.01812203125,90.82376796875Q19.85201203125,90.91868796875,19.69426203125,91.01814796875Q19.53650203125,91.11760796875,19.38747203125,91.22137796875Q19.23844203125,91.32513796875,19.09850203125,91.43295796875Q18.95856203125,91.54077796875,18.82805203125,91.65239796875Q18.69754203125,91.76400796875,18.57677203125,91.87914796875Q18.45600203125,91.99428796875,18.34527203125,92.11266796875Q18.23453203125,92.23105796875,18.13409103125,92.35240796875Q18.03365203125,92.47375796875,17.94375403125,92.59777796875Q17.85385703125,92.72178796875,17.77471603125,92.84817796875Q17.69557603125,92.97456796875,17.62738403125,93.10302796875Q17.55919203125,93.23148796875,17.50211303125,93.36169796875Q17.44503403125,93.49190796875,17.39920403125,93.62356796875Q17.35337503125,93.75522796875,17.31890603125,93.88800796875Q17.28443663125,94.02079796875,17.26141103125,94.15438796875Q17.23838543125,94.28797796875,17.22685873125,94.42204796875001Q17.21533203125,94.55612796875,17.21533203125,94.69035796874999Q17.21533203125,94.82459796875,17.22685873125,94.95866796875Q17.23838543125,95.09274796875,17.26141103125,95.22633796875Q17.28443663125,95.35992796875,17.31890603125,95.49270796875Q17.35337503125,95.62549796875,17.39920403125,95.75714796875Q17.44503403125,95.88880796875,17.50211303125,96.01901796875Q17.55919203125,96.14923796875,17.62738403125,96.27768796875Q17.69557603125,96.40614796875,17.77471603125,96.53253796875Q17.85385703125,96.65892796874999,17.94375403125,96.78294796875Q18.03365203125,96.90696796875,18.13409103125,97.02830796875Q18.23453203125,97.14965796875,18.34527203125,97.26804796875Q18.45600203125,97.38642796875,18.57677203125,97.50156796875Q18.69754203125,97.61670796875,18.82805203125,97.72832796875Q18.95856203125,97.83993796875001,19.09850203125,97.94775796875Q19.23844203125,98.05557796875,19.38747203125,98.15934796875Q19.53650203125,98.26310796875,19.69426203125,98.36257796875Q19.85201203125,98.46203796875,20.01812203125,98.55695796875Q20.18423203125,98.65187796875,20.35829203125,98.74202796875Q20.53235203125,98.83216796875,20.71394203125,98.91732796875Q20.89553203125,99.00248796875,21.08422203125,99.08244796875Q21.27290203125,99.16241796875,21.46823203125,99.23696796875Q21.66355203125,99.31156796875,21.86504203125,99.38056796875Q22.066542031250002,99.44956796875,22.27371203125,99.51286796875Q22.48089203125,99.57616796875,22.69325203125,99.63356796875Q22.90561203125,99.69096796875,23.12264203125,99.74226796875Q23.33967203125,99.79366796875,23.56085203125,99.83886796875Q23.78204203125,99.88416796875,24.00683203125,99.92306796875Q24.23163203125,99.96206796875,24.45951203125,99.99466796875001Q24.68738203125,100.02726796875,24.91778203125,100.05346796875Q25.148182031250002,100.07966796875,25.380552031249998,100.09936796875Q25.61292203125,100.11906796875,25.84671203125,100.13226796875Q26.08049203125,100.14536796875,26.31512203125,100.15196796875Q26.54975203125,100.15856796875,26.78467203125,100.15856796875ZM32.90623203125,91.95247796875Q35.05403203125,93.17977796875,35.05403203125,94.69035796874999Q35.05403203125,96.20093796875,32.90623203125,97.42823796875Q30.40323203125,98.85854796875,26.78467203125,98.85854796875Q23.16615203125,98.85854796875,20.66310203125,97.42823796875Q18.51533203125,96.20093796875,18.51533203125,94.69035796874999Q18.51533203125,93.17977796875,20.66310203125,91.95247796875Q23.16615203125,90.52216796875,26.78467203125,90.52216796875Q30.40313203125,90.52216796875,32.90623203125,91.95247796875Z"
            fill-rule="evenodd" fill="var(--color)" fill-opacity="1" />
        </g>
      </g>
      <g>
        <g filter="url(#${lineName}2_223_09600)">
          <ellipse cx="26.760255813598633" cy="94.6878113746643" rx="13.471681594848633"
            ry="7.465643405914307" fill-opacity="0" stroke-opacity="0.6000000238418579"
            stroke="#FFFFFF" fill="none" stroke-width="0.30000001192092896" />
        </g>
        <g filter="url(#${lineName}3_223_09601)">
          <ellipse cx="26.76008415222168" cy="94.65105485916138" rx="9.05744743347168"
            ry="4.920097827911377" fill-opacity="0" stroke-opacity="0.6000000238418579"
            stroke="#FFFFFF" fill="none" stroke-width="0.30000001192092896" />
        </g>
      </g>
      <g filter="url(#${lineName}4_223_09602)">
        <rect x="25.78466796875" y="53.6904296875" width="2" height="40" rx="0"
          fill="url(#${lineName}5_223_14931)" fill-opacity="1" />
      </g>
      <g filter="url(#${lineName}6_223_09603)">
        <rect x="25.78466796875" y="53.6904296875" width="2" height="40" rx="0"
          fill="url(#${lineName}7_223_14931)" fill-opacity="1" />
      </g>
      <g>
        <rect x="26.28466796875" y="53.6904296875" width="1" height="40" rx="0"
          fill="url(#${lineName}8_1_1582)" fill-opacity="0.5" />
      </g>
      <g filter="url(#${lineName}9_223_09605)">
        <ellipse cx="26.78466796875" cy="94.69063901901245" rx="5" ry="2.857142925262451"
          fill="url(#${lineName}a_223_14967)" fill-opacity="1" />
      </g>
    </g>
    <g>
      <g style="opacity:0.5;" filter="url(#${lineName}b_223_09607)">
        <path
          d="M46.56926796875,17.1130296875Q46.68276796875,17.1786296875,46.78676796875,17.258429687499998Q46.89076796875,17.3382296875,46.98346796875,17.4309296875Q47.07616796875,17.523629687499998,47.15596796875,17.6276296875Q47.23576796875,17.7316296875,47.30136796875,17.8451296875Q47.36686796875,17.9586296875,47.41706796875,18.0797296875Q47.46716796875,18.2008296875,47.50116796875,18.3275296875Q47.53506796875,18.4541296875,47.55216796875,18.5840296875Q47.56926796875,18.714029687500002,47.56926796875,18.8451296875L47.56926796875,40.5357296875Q47.56926796875,40.6668296875,47.55216796875,40.7968296875Q47.53506796875,40.9267296875,47.50116796875,41.0533296875Q47.46716796875,41.1800296875,47.41706796875,41.3011296875Q47.36686796875,41.4222296875,47.30136796875,41.5357296875Q47.23576796875,41.6492296875,47.15596796875,41.7532296875Q47.07616796875,41.8572296875,46.98346796875,41.9499296875Q46.89076796875,42.0426296875,46.78676796875,42.1224296875Q46.68276796875,42.2022296875,46.56926796875,42.2678296875L27.78466796875,53.1130296875Q27.67116796875,53.1786296875,27.55006796875,53.2288296875Q27.42896796875,53.2789296875,27.30226796875,53.3129296875Q27.17566796875,53.3468296875,27.04576796875,53.3639296875Q26.91576796875,53.3810296875,26.78466796875,53.3810296875Q26.65356796875,53.3810296875,26.52356796875,53.3639296875Q26.39366796875,53.3468296875,26.26706796875,53.3129296875Q26.14036796875,53.2789296875,26.01926796875,53.2288296875Q25.89816796875,53.1786296875,25.78466796875,53.1130296875L7.00005796875,42.2678296875Q6.88653796875,42.2022296875,6.78253796875,42.1224296875Q6.67853796875,42.0426296875,6.5858479687500004,41.9499296875Q6.49314796875,41.8572296875,6.41334796875,41.7532296875Q6.3335479687500005,41.6492296875,6.26800796875,41.5357296875Q6.20246796875,41.4222296875,6.15229796875,41.3011296875Q6.10213796875,41.1800296875,6.06820796875,41.0533296875Q6.0342779687500006,40.9267296875,6.01716796875,40.7968296875Q6.00005796875,40.6668296875,6.00005796875,40.5357296875L6.00005796875,18.8451296875Q6.00005796875,18.714029687500002,6.01716796875,18.5840296875Q6.0342779687500006,18.4541296875,6.06820796875,18.3275296875Q6.10213796875,18.2008296875,6.15229796875,18.0797296875Q6.20246796875,17.9586296875,6.26800796875,17.8451296875Q6.3335479687500005,17.7316296875,6.41334796875,17.6276296875Q6.49314796875,17.523629687499998,6.5858479687500004,17.4309296875Q6.67853796875,17.3382296875,6.78253796875,17.258429687499998Q6.88653796875,17.1786296875,7.00005796875,17.1131296875L25.78466796875,6.2677796875Q25.89816796875,6.2022366875,26.01926796875,6.1520716875Q26.14036796875,6.1019066875,26.26706796875,6.0679786875Q26.39366796875,6.0340516875,26.52356796875,6.0169406875Q26.65356796875,5.9998306875,26.78466796875,5.9998306875Q26.91576796875,5.9998306875,27.04576796875,6.0169406875Q27.17566796875,6.0340516875,27.30226796875,6.0679786875Q27.42896796875,6.1019066875,27.55006796875,6.1520716875Q27.67116796875,6.2022366875,27.78466796875,6.2677796875L46.56926796875,17.1130296875Z"
          fill="url(#${lineName}c_223_14448)" fill-opacity="1" />
        <path
          d="M46.56926796875,17.1130296875Q46.68276796875,17.1786296875,46.78676796875,17.258429687499998Q46.89076796875,17.3382296875,46.98346796875,17.4309296875Q47.07616796875,17.523629687499998,47.15596796875,17.6276296875Q47.23576796875,17.7316296875,47.30136796875,17.8451296875Q47.36686796875,17.9586296875,47.41706796875,18.0797296875Q47.46716796875,18.2008296875,47.50116796875,18.3275296875Q47.53506796875,18.4541296875,47.55216796875,18.5840296875Q47.56926796875,18.714029687500002,47.56926796875,18.8451296875L47.56926796875,40.5357296875Q47.56926796875,40.6668296875,47.55216796875,40.7968296875Q47.53506796875,40.9267296875,47.50116796875,41.0533296875Q47.46716796875,41.1800296875,47.41706796875,41.3011296875Q47.36686796875,41.4222296875,47.30136796875,41.5357296875Q47.23576796875,41.6492296875,47.15596796875,41.7532296875Q47.07616796875,41.8572296875,46.98346796875,41.9499296875Q46.89076796875,42.0426296875,46.78676796875,42.1224296875Q46.68276796875,42.2022296875,46.56926796875,42.2678296875L27.78466796875,53.1130296875Q27.67116796875,53.1786296875,27.55006796875,53.2288296875Q27.42896796875,53.2789296875,27.30226796875,53.3129296875Q27.17566796875,53.3468296875,27.04576796875,53.3639296875Q26.91576796875,53.3810296875,26.78466796875,53.3810296875Q26.65356796875,53.3810296875,26.52356796875,53.3639296875Q26.39366796875,53.3468296875,26.26706796875,53.3129296875Q26.14036796875,53.2789296875,26.01926796875,53.2288296875Q25.89816796875,53.1786296875,25.78466796875,53.1130296875L7.00005796875,42.2678296875Q6.88653796875,42.2022296875,6.78253796875,42.1224296875Q6.67853796875,42.0426296875,6.5858479687500004,41.9499296875Q6.49314796875,41.8572296875,6.41334796875,41.7532296875Q6.3335479687500005,41.6492296875,6.26800796875,41.5357296875Q6.20246796875,41.4222296875,6.15229796875,41.3011296875Q6.10213796875,41.1800296875,6.06820796875,41.0533296875Q6.0342779687500006,40.9267296875,6.01716796875,40.7968296875Q6.00005796875,40.6668296875,6.00005796875,40.5357296875L6.00005796875,18.8451296875Q6.00005796875,18.714029687500002,6.01716796875,18.5840296875Q6.0342779687500006,18.4541296875,6.06820796875,18.3275296875Q6.10213796875,18.2008296875,6.15229796875,18.0797296875Q6.20246796875,17.9586296875,6.26800796875,17.8451296875Q6.3335479687500005,17.7316296875,6.41334796875,17.6276296875Q6.49314796875,17.523629687499998,6.5858479687500004,17.4309296875Q6.67853796875,17.3382296875,6.78253796875,17.258429687499998Q6.88653796875,17.1786296875,7.00005796875,17.1131296875L25.78466796875,6.2677796875Q25.89816796875,6.2022366875,26.01926796875,6.1520716875Q26.14036796875,6.1019066875,26.26706796875,6.0679786875Q26.39366796875,6.0340516875,26.52356796875,6.0169406875Q26.65356796875,5.9998306875,26.78466796875,5.9998306875Q26.91576796875,5.9998306875,27.04576796875,6.0169406875Q27.17566796875,6.0340516875,27.30226796875,6.0679786875Q27.42896796875,6.1019066875,27.55006796875,6.1520716875Q27.67116796875,6.2022366875,27.78466796875,6.2677796875L46.56926796875,17.1130296875Z"
          fill="url(#${lineName}d_223_14449)" fill-opacity="1" />
      </g>
      <g>
        <path
          d="M37.90906796875,36.1130796875Q38.022567968749996,36.1786196875,38.126567968749995,36.2584196875Q38.23056796875,36.3382196875,38.32326796875,36.4309196875Q38.415967968749996,36.5236096875,38.49576796875,36.6276096875Q38.57556796875,36.7316096875,38.64106796875,36.8451296875Q38.70666796875,36.9586596875,38.75676796875,37.0797596875Q38.80696796875,37.2008696875,38.840867968750004,37.3274896875Q38.87476796875,37.4541096875,38.89186796875,37.5840796875Q38.90906796875,37.7140396875,38.90906796875,37.8451296875L38.90906796875,49.5357296875Q38.90906796875,49.6668296875,38.89186796875,49.796729687500005Q38.87476796875,49.9267296875,38.840867968750004,50.053329687499996Q38.80696796875,50.180029687499996,38.75676796875,50.3011296875Q38.70666796875,50.4222296875,38.64106796875,50.5357296875Q38.57556796875,50.6492296875,38.49576796875,50.753229687499996Q38.415967968749996,50.857229687499995,38.32326796875,50.9499296875Q38.23056796875,51.042629687499996,38.126567968749995,51.1224296875Q38.022567968749996,51.2022296875,37.90906796875,51.2678296875L27.78466796875,57.1130296875Q27.671167968749998,57.1786296875,27.55006796875,57.228829687499996Q27.42896796875,57.278929687499996,27.30226796875,57.3129296875Q27.17566796875,57.346829687500005,27.04576796875,57.363929687500004Q26.91576796875,57.3810296875,26.78466796875,57.3810296875Q26.65356796875,57.3810296875,26.52356796875,57.363929687500004Q26.39366796875,57.346829687500005,26.26706796875,57.3129296875Q26.14036796875,57.278929687499996,26.01926796875,57.228829687499996Q25.898167968750002,57.1786296875,25.78466796875,57.1130296875L15.66030796875,51.2678296875Q15.54678796875,51.2022296875,15.44278796875,51.1224296875Q15.338787968750001,51.042629687499996,15.24609796875,50.9499296875Q15.153407968749999,50.857229687499995,15.07360796875,50.753229687499996Q14.99380796875,50.6492296875,14.92825796875,50.5357296875Q14.862717968750001,50.4222296875,14.81255796875,50.3011296875Q14.76238796875,50.180029687499996,14.72845796875,50.053329687499996Q14.69453796875,49.9267296875,14.67741796875,49.796729687500005Q14.66030796875,49.6668296875,14.66030796875,49.5357296875L14.66030796875,37.8451296875Q14.66030796875,37.7140396875,14.67741796875,37.5840796875Q14.69452796875,37.4541096875,14.72845796875,37.3274896875Q14.76238796875,37.2008696875,14.81255796875,37.0797596875Q14.862717968750001,36.9586596875,14.92825796875,36.8451296875Q14.99380796875,36.7316096875,15.07360796875,36.6276096875Q15.153407968749999,36.5236096875,15.24609796875,36.4309196875Q15.338787968750001,36.338229687500004,15.44278796875,36.2584296875Q15.54678796875,36.1786196875,15.66030796875,36.1130796875L25.78466796875,30.2677796875Q25.898167968750002,30.2022366875,26.01926796875,30.1520716875Q26.14036796875,30.1019066875,26.26706796875,30.0679786875Q26.39366796875,30.0340516875,26.52356796875,30.0169406875Q26.65356796875,29.9998306875,26.78466796875,29.9998306875Q26.91576796875,29.9998306875,27.04576796875,30.0169406875Q27.17566796875,30.0340516875,27.30226796875,30.0679786875Q27.42896796875,30.1019066875,27.55006796875,30.1520716875Q27.671167968749998,30.2022366875,27.78466796875,30.2677796875L37.90906796875,36.1130796875Z"
          fill="#09173A" fill-opacity="0.30000001192092896" />
        <path
          d="M37.90906796875,36.1130796875Q38.022567968749996,36.1786196875,38.126567968749995,36.2584196875Q38.23056796875,36.3382196875,38.32326796875,36.4309196875Q38.415967968749996,36.5236096875,38.49576796875,36.6276096875Q38.57556796875,36.7316096875,38.64106796875,36.8451296875Q38.70666796875,36.9586596875,38.75676796875,37.0797596875Q38.80696796875,37.2008696875,38.840867968750004,37.3274896875Q38.87476796875,37.4541096875,38.89186796875,37.5840796875Q38.90906796875,37.7140396875,38.90906796875,37.8451296875L38.90906796875,49.5357296875Q38.90906796875,49.6668296875,38.89186796875,49.796729687500005Q38.87476796875,49.9267296875,38.840867968750004,50.053329687499996Q38.80696796875,50.180029687499996,38.75676796875,50.3011296875Q38.70666796875,50.4222296875,38.64106796875,50.5357296875Q38.57556796875,50.6492296875,38.49576796875,50.753229687499996Q38.415967968749996,50.857229687499995,38.32326796875,50.9499296875Q38.23056796875,51.042629687499996,38.126567968749995,51.1224296875Q38.022567968749996,51.2022296875,37.90906796875,51.2678296875L27.78466796875,57.1130296875Q27.671167968749998,57.1786296875,27.55006796875,57.228829687499996Q27.42896796875,57.278929687499996,27.30226796875,57.3129296875Q27.17566796875,57.346829687500005,27.04576796875,57.363929687500004Q26.91576796875,57.3810296875,26.78466796875,57.3810296875Q26.65356796875,57.3810296875,26.52356796875,57.363929687500004Q26.39366796875,57.346829687500005,26.26706796875,57.3129296875Q26.14036796875,57.278929687499996,26.01926796875,57.228829687499996Q25.898167968750002,57.1786296875,25.78466796875,57.1130296875L15.66030796875,51.2678296875Q15.54678796875,51.2022296875,15.44278796875,51.1224296875Q15.338787968750001,51.042629687499996,15.24609796875,50.9499296875Q15.153407968749999,50.857229687499995,15.07360796875,50.753229687499996Q14.99380796875,50.6492296875,14.92825796875,50.5357296875Q14.862717968750001,50.4222296875,14.81255796875,50.3011296875Q14.76238796875,50.180029687499996,14.72845796875,50.053329687499996Q14.69453796875,49.9267296875,14.67741796875,49.796729687500005Q14.66030796875,49.6668296875,14.66030796875,49.5357296875L14.66030796875,37.8451296875Q14.66030796875,37.7140396875,14.67741796875,37.5840796875Q14.69452796875,37.4541096875,14.72845796875,37.3274896875Q14.76238796875,37.2008696875,14.81255796875,37.0797596875Q14.862717968750001,36.9586596875,14.92825796875,36.8451296875Q14.99380796875,36.7316096875,15.07360796875,36.6276096875Q15.153407968749999,36.5236096875,15.24609796875,36.4309196875Q15.338787968750001,36.338229687500004,15.44278796875,36.2584296875Q15.54678796875,36.1786196875,15.66030796875,36.1130796875L25.78466796875,30.2677796875Q25.898167968750002,30.2022366875,26.01926796875,30.1520716875Q26.14036796875,30.1019066875,26.26706796875,30.0679786875Q26.39366796875,30.0340516875,26.52356796875,30.0169406875Q26.65356796875,29.9998306875,26.78466796875,29.9998306875Q26.91576796875,29.9998306875,27.04576796875,30.0169406875Q27.17566796875,30.0340516875,27.30226796875,30.0679786875Q27.42896796875,30.1019066875,27.55006796875,30.1520716875Q27.671167968749998,30.2022366875,27.78466796875,30.2677796875L37.90906796875,36.1130796875Z"
          fill="url(#${lineName}e_223_14502)" fill-opacity="1" />
        <path
          d="M37.90906796875,36.1130796875Q38.022567968749996,36.1786196875,38.126567968749995,36.2584196875Q38.23056796875,36.3382196875,38.32326796875,36.4309196875Q38.415967968749996,36.5236096875,38.49576796875,36.6276096875Q38.57556796875,36.7316096875,38.64106796875,36.8451296875Q38.70666796875,36.9586596875,38.75676796875,37.0797596875Q38.80696796875,37.2008696875,38.840867968750004,37.3274896875Q38.87476796875,37.4541096875,38.89186796875,37.5840796875Q38.90906796875,37.7140396875,38.90906796875,37.8451296875L38.90906796875,49.5357296875Q38.90906796875,49.6668296875,38.89186796875,49.796729687500005Q38.87476796875,49.9267296875,38.840867968750004,50.053329687499996Q38.80696796875,50.180029687499996,38.75676796875,50.3011296875Q38.70666796875,50.4222296875,38.64106796875,50.5357296875Q38.57556796875,50.6492296875,38.49576796875,50.753229687499996Q38.415967968749996,50.857229687499995,38.32326796875,50.9499296875Q38.23056796875,51.042629687499996,38.126567968749995,51.1224296875Q38.022567968749996,51.2022296875,37.90906796875,51.2678296875L27.78466796875,57.1130296875Q27.671167968749998,57.1786296875,27.55006796875,57.228829687499996Q27.42896796875,57.278929687499996,27.30226796875,57.3129296875Q27.17566796875,57.346829687500005,27.04576796875,57.363929687500004Q26.91576796875,57.3810296875,26.78466796875,57.3810296875Q26.65356796875,57.3810296875,26.52356796875,57.363929687500004Q26.39366796875,57.346829687500005,26.26706796875,57.3129296875Q26.14036796875,57.278929687499996,26.01926796875,57.228829687499996Q25.898167968750002,57.1786296875,25.78466796875,57.1130296875L15.66030796875,51.2678296875Q15.54678796875,51.2022296875,15.44278796875,51.1224296875Q15.338787968750001,51.042629687499996,15.24609796875,50.9499296875Q15.153407968749999,50.857229687499995,15.07360796875,50.753229687499996Q14.99380796875,50.6492296875,14.92825796875,50.5357296875Q14.862717968750001,50.4222296875,14.81255796875,50.3011296875Q14.76238796875,50.180029687499996,14.72845796875,50.053329687499996Q14.69453796875,49.9267296875,14.67741796875,49.796729687500005Q14.66030796875,49.6668296875,14.66030796875,49.5357296875L14.66030796875,37.8451296875Q14.66030796875,37.7140396875,14.67741796875,37.5840796875Q14.69452796875,37.4541096875,14.72845796875,37.3274896875Q14.76238796875,37.2008696875,14.81255796875,37.0797596875Q14.862717968750001,36.9586596875,14.92825796875,36.8451296875Q14.99380796875,36.7316096875,15.07360796875,36.6276096875Q15.153407968749999,36.5236096875,15.24609796875,36.4309196875Q15.338787968750001,36.338229687500004,15.44278796875,36.2584296875Q15.54678796875,36.1786196875,15.66030796875,36.1130796875L25.78466796875,30.2677796875Q25.898167968750002,30.2022366875,26.01926796875,30.1520716875Q26.14036796875,30.1019066875,26.26706796875,30.0679786875Q26.39366796875,30.0340516875,26.52356796875,30.0169406875Q26.65356796875,29.9998306875,26.78466796875,29.9998306875Q26.91576796875,29.9998306875,27.04576796875,30.0169406875Q27.17566796875,30.0340516875,27.30226796875,30.0679786875Q27.42896796875,30.1019066875,27.55006796875,30.1520716875Q27.671167968749998,30.2022366875,27.78466796875,30.2677796875L37.90906796875,36.1130796875Z"
          fill="url(#${lineName}f_223_14503)" fill-opacity="1" />
        <path
          d="M38.90906796875,37.8451296875Q38.90906796875,37.7140396875,38.89186796875,37.5840796875Q38.87476796875,37.4541096875,38.840867968750004,37.3274896875Q38.80696796875,37.2008696875,38.75676796875,37.0797596875Q38.70666796875,36.9586596875,38.64106796875,36.8451296875Q38.57556796875,36.7316096875,38.49576796875,36.6276096875Q38.415967968749996,36.5236096875,38.32326796875,36.4309196875Q38.23056796875,36.3382196875,38.126567968749995,36.2584196875Q38.022567968749996,36.1786196875,37.90906796875,36.1130796875L27.78466796875,30.2677796875Q27.671167968749998,30.2022366875,27.55006796875,30.1520716875Q27.42896796875,30.1019066875,27.30226796875,30.0679786875Q27.17566796875,30.0340516875,27.04576796875,30.0169406875Q26.91576796875,29.9998306875,26.78466796875,29.9998306875Q26.65356796875,29.9998306875,26.52356796875,30.0169406875Q26.39366796875,30.0340516875,26.26706796875,30.0679786875Q26.14036796875,30.1019066875,26.01926796875,30.1520716875Q25.898167968750002,30.2022366875,25.78466796875,30.2677796875L15.66030796875,36.1130796875Q15.54678796875,36.1786196875,15.44278796875,36.2584296875Q15.338787968750001,36.338229687500004,15.24609796875,36.4309196875Q15.153407968749999,36.5236096875,15.07360796875,36.6276096875Q14.99380796875,36.7316096875,14.92825796875,36.8451296875Q14.862717968750001,36.9586596875,14.81255796875,37.0797596875Q14.76238796875,37.2008696875,14.72845796875,37.3274896875Q14.69452796875,37.4541096875,14.67741796875,37.5840796875Q14.66030796875,37.7140396875,14.66030796875,37.8451296875L14.66030796875,49.5357296875Q14.66030796875,49.6668296875,14.67741796875,49.796729687500005Q14.69453796875,49.9267296875,14.72845796875,50.053329687499996Q14.76238796875,50.180029687499996,14.81255796875,50.3011296875Q14.862717968750001,50.4222296875,14.92825796875,50.5357296875Q14.99380796875,50.6492296875,15.07360796875,50.753229687499996Q15.153407968749999,50.857229687499995,15.24609796875,50.9499296875Q15.338787968750001,51.042629687499996,15.44278796875,51.1224296875Q15.54678796875,51.2022296875,15.66030796875,51.2678296875L25.78466796875,57.1130296875Q25.898167968750002,57.1786296875,26.01926796875,57.228829687499996Q26.14036796875,57.278929687499996,26.26706796875,57.3129296875Q26.39366796875,57.346829687500005,26.52356796875,57.363929687500004Q26.65356796875,57.3810296875,26.78466796875,57.3810296875Q26.91576796875,57.3810296875,27.04576796875,57.363929687500004Q27.17566796875,57.346829687500005,27.30226796875,57.3129296875Q27.42896796875,57.278929687499996,27.55006796875,57.228829687499996Q27.671167968749998,57.1786296875,27.78466796875,57.1130296875L37.90906796875,51.2678296875Q38.022567968749996,51.2022296875,38.126567968749995,51.1224296875Q38.23056796875,51.042629687499996,38.32326796875,50.9499296875Q38.415967968749996,50.857229687499995,38.49576796875,50.753229687499996Q38.57556796875,50.6492296875,38.64106796875,50.5357296875Q38.70666796875,50.4222296875,38.75676796875,50.3011296875Q38.80696796875,50.180029687499996,38.840867968750004,50.053329687499996Q38.87476796875,49.9267296875,38.89186796875,49.796729687500005Q38.90906796875,49.6668296875,38.90906796875,49.5357296875L38.90906796875,37.8451296875ZM27.03466796875,31.5668196875L37.15906796875,37.4121196875Q37.40906796875,37.5564596875,37.40906796875,37.8451296875L37.40906796875,49.5357296875Q37.40906796875,49.8244296875,37.15906796875,49.9687296875L27.03466796875,55.814029687499996Q26.78466796875,55.9583296875,26.53466796875,55.814029687499996L16.41030796875,49.9687296875Q16.16030796875,49.8244296875,16.16030796875,49.5357296875L16.16030796875,37.8451296875Q16.16030796875,37.5564596875,16.41030796875,37.4121196875L26.53466796875,31.5668196875Q26.78466796875,31.4224796875,27.03466796875,31.5668196875Z"
          fill-rule="evenodd" fill="url(#${lineName}g_1_1967)" fill-opacity="1" />
      </g>
      <g filter="url(#${lineName}h_223_09609)">
        <path
          d="M5.78466115952,28.6906599705L5.78466153145,41.4356296875L26.78466796875,53.6905296875L47.78466796875,41.4356296875L47.78466796875,28.6906787175L26.78466796875,40.9455296875L5.78466115952,28.6906599705Z"
          fill-rule="evenodd" fill="url(#${lineName}i_223_14515)" fill-opacity="1" />
      </g>
      <g>
        <path
          d="M48.28476796875,39.7128296875L48.28476796875,28.6906430995Q48.28476796875,28.6413973875,48.27506796875,28.5930979875Q48.26546796875,28.5447986875,48.24666796875,28.4993016875Q48.22776796875,28.4538046875,48.20046796875,28.4128576875Q48.17306796875,28.3719116875,48.13826796875,28.3370896875Q48.10346796875,28.3022676875,48.06246796875,28.2749086875Q48.02156796875,28.2475486875,47.97606796875,28.2287036875Q47.93056796875,28.2098576875,47.88226796875,28.2002506875Q47.83396796875,28.1906426875,47.78476796875,28.1906426875Q47.73546796875,28.1906426875,47.68716796875,28.2002506875Q47.63886796875,28.2098576875,47.59336796875,28.2287036875Q47.54786796875,28.2475486875,47.50696796875,28.2749086875Q47.46596796875,28.3022676875,47.43116796875,28.3370896875Q47.39636796875,28.3719116875,47.36896796875,28.4128576875Q47.34166796875,28.4538046875,47.32276796875,28.4993016875Q47.30396796875,28.5447986875,47.29436796875,28.5930979875Q47.28476796875,28.6413973875,47.28476796875,28.6906430995L47.28476796875,39.7128296875Q47.28476796875,41.1485296875,46.04476796875,41.8720296875L28.04476796875,52.3762296875Q26.78476796875,53.1116296875,25.52466796875,52.3762296875L7.52466796875,41.8720296875Q6.28472296875,41.1484296875,6.28472296875,39.7128296875L6.28472196875,28.6906955735Q6.28472196875,28.6414498875,6.27511496875,28.5931503875Q6.26550696875,28.5448506875,6.24666196875,28.4993536875Q6.22781596875,28.4538566875,6.20045696875,28.4129106875Q6.17309796875,28.3719646875,6.13827596875,28.3371426875Q6.10345396875,28.3023206875,6.06250696875,28.2749606875Q6.02156096875,28.2476016875,5.97606396875,28.2287556875Q5.93056696875,28.2099106875,5.88226736875,28.2003026875Q5.83396786875,28.1906956875,5.78472218005,28.1906956875Q5.68526596875,28.1906956875,5.59337996875,28.2287556875Q5.50149496875,28.2668156875,5.43116896875,28.3371426875Q5.36084296875,28.4074686875,5.32278196875,28.4993536875Q5.28472196875,28.5912393875,5.28472196875,28.6906956025L5.28472296875,39.7128296875Q5.28472196875,41.7227296875,7.02064796875,42.7357296875L25.02066796875,53.239929687499995Q26.78476796875,54.2694296875,28.54876796875,53.239929687499995L46.54876796875,42.7357296875Q48.28476796875,41.7227296875,48.28476796875,39.7128296875Z"
          fill-rule="evenodd" fill="url(#${lineName}j_1_1954)" fill-opacity="1" />
      </g>
      <g>
        <path
          d="M18.7411,9.7570096875L18.7404,9.7574196875Q18.6834,9.7903296875,18.6368,9.8368796875Q18.5903,9.8834196875,18.5574,9.9404296875Q18.5245,9.9974396875,18.5074,10.0610196875Q18.4904,10.1245996875,18.4904,10.1904296875Q18.4904,10.2396796875,18.5,10.2879796875Q18.5096,10.3362696875,18.528399999999998,10.3817696875Q18.5473,10.4272696875,18.5746,10.4682196875Q18.602,10.5091596875,18.6368,10.5439796875Q18.671599999999998,10.5788096875,18.712600000000002,10.6061596875Q18.753500000000003,10.6335196875,18.799,10.6523696875Q18.8445,10.6712096875,18.8928,10.6808196875Q18.9411,10.6904296875,18.9904,10.6904296875Q19.1244,10.6904296875,19.2404,10.6234396875L21.8385,9.1234396875L24.4365,7.6234396874999995L25.5346,6.9894696875Q26.7846,6.2677816875,28.0346,6.9894696875L31.7308,9.1234396875L34.3288,10.6234396875Q34.444900000000004,10.6904296875,34.5788,10.6904296875Q34.6281,10.6904296875,34.6764,10.6808196875Q34.7247,10.6712096875,34.7702,10.6523696875Q34.8157,10.6335196875,34.8566,10.6061596875Q34.8976,10.5787996875,34.9324,10.5439796875Q34.9672,10.5091596875,34.9946,10.4682096875Q35.0219,10.4272696875,35.040800000000004,10.3817696875Q35.0596,10.3362696875,35.069199999999995,10.2879796875Q35.0788,10.2396796875,35.0788,10.1904296875Q35.0788,10.1245996875,35.061800000000005,10.0610196875Q35.044799999999995,9.9974396875,35.0119,9.9404296875Q34.978899999999996,9.8834196875,34.9324,9.8368796875Q34.8858,9.7903296875,34.8288,9.7574196875L34.8285,9.7571996875L32.2308,8.2574196875L28.5346,6.1234426875Q26.7846,5.1130816875,25.0346,6.1234426875L23.9365,6.7574196875000005L21.3385,8.2574196875L18.7411,9.7570096875ZM40.0254,12.7576296875L42.6231,14.2574196875L46.3192,16.3914296875Q48.0692,17.4017296875,48.0692,19.422529687500003L48.0692,26.6904296875Q48.0692,26.7396296875,48.0596,26.7879296875Q48.05,26.8362296875,48.0312,26.8817296875Q48.0123,26.9272296875,47.9849,26.9682296875Q47.9576,27.0091296875,47.9228,27.0440296875Q47.888,27.0788296875,47.847,27.1061296875Q47.8061,27.1335296875,47.7606,27.1523296875Q47.7151,27.1712296875,47.6668,27.1808296875Q47.6185,27.1904296875,47.5692,27.1904296875Q47.52,27.1904296875,47.4717,27.1808296875Q47.4234,27.1712296875,47.3779,27.1523296875Q47.3324,27.1335296875,47.2914,27.1061296875Q47.2505,27.0788296875,47.2157,27.0440296875Q47.1808,27.0091296875,47.1535,26.9682296875Q47.1261,26.9272296875,47.1073,26.8817296875Q47.0884,26.8362296875,47.0788,26.7879296875Q47.0692,26.7396296875,47.0692,26.6904296875L47.0692,26.6884296875L47.0692,19.422529687500003Q47.0692,17.9791296875,45.8192,17.2574296875L42.1231,15.1234396875L39.525,13.6234396875Q39.468,13.5905296875,39.4214,13.5439796875Q39.3749,13.4974396875,39.342,13.4404296875Q39.3091,13.3834196875,39.292,13.3198396875Q39.275,13.2562596875,39.275,13.1904296875Q39.275,13.1411796875,39.2846,13.0928796875Q39.2942,13.0445796875,39.313,12.9990896875Q39.3319,12.9535896875,39.3592,12.9126396875Q39.3866,12.8716996875,39.4214,12.8368796875Q39.4563,12.8020496875,39.4972,12.7746896875Q39.5381,12.747329687499999,39.5836,12.7284896875Q39.6291,12.709639687500001,39.6774,12.7000396875Q39.7257,12.6904296875,39.775,12.6904296875Q39.909,12.6904296875,40.025,12.7574196875L40.0254,12.7576296875ZM14.04423,13.6234396875Q14.10124,13.5905296875,14.14778,13.5439896875Q14.19433,13.4974396875,14.22724,13.4404296875Q14.26015,13.3834196875,14.27719,13.3198396875Q14.29423,13.2562596875,14.29423,13.1904296875Q14.29423,13.141189687499999,14.28462,13.0928896875Q14.27501,13.0445896875,14.25617,12.9990896875Q14.23732,12.9535896875,14.20996,12.9126496875Q14.1826,12.8716996875,14.14778,12.8368796875Q14.11296,12.8020596875,14.07201,12.7746996875Q14.03107,12.7473396875,13.98557,12.7284896875Q13.94007,12.7096496875,13.891770000000001,12.7000396875Q13.84347,12.6904296875,13.794229999999999,12.6904296875Q13.66025,12.6904296875,13.544229999999999,12.7574196875L13.54363,12.7577696875L10.94615,14.2574196875L8.34808,15.7574296875L7.25,16.3914296875Q5.5,17.4017296875,5.5,19.422529687500003L5.5,23.6904296875L5.5,26.6904296875Q5.5,26.7396296875,5.509608,26.7879296875Q5.519215,26.8362296875,5.53806,26.8817296875Q5.556906,26.9272296875,5.584265,26.9682296875Q5.611625,27.0091296875,5.646447,27.0440296875Q5.681269,27.0788296875,5.722215,27.1061296875Q5.763161,27.1335296875,5.808658,27.1523296875Q5.854155,27.1712296875,5.9024549,27.1808296875Q5.9507543,27.1904296875,6,27.1904296875Q6.0492457,27.1904296875,6.0975452,27.1808296875Q6.145845,27.1712296875,6.191342,27.1523296875Q6.236839,27.1335296875,6.277785,27.1061296875Q6.318731,27.0788296875,6.353553,27.0440296875Q6.388375,27.0091296875,6.415735,26.9682296875Q6.443094,26.9272296875,6.46194,26.8817296875Q6.480785,26.8362296875,6.490393,26.7879296875Q6.5,26.7396296875,6.5,26.6904296875L6.5,23.6904296875L6.5,19.422529687500003Q6.5,17.9791296875,7.75,17.2574296875L8.84808,16.6234296875L11.44615,15.1234396875L14.04423,13.6234396875Z"
          fill-rule="evenodd" fill="url(#${lineName}k_223_14471)" fill-opacity="1" />
      </g>
      <g>
        <path
          d="M44.83726796875,18.1130296875Q44.95076796875,18.1786296875,45.05476796875,18.258429687499998Q45.15876796875,18.3382296875,45.25146796875,18.4309296875Q45.34416796875,18.523629687499998,45.42396796875,18.6276296875Q45.50376796875,18.7316296875,45.56926796875,18.8451296875Q45.63486796875,18.9586296875,45.68496796875,19.0797296875Q45.73516796875,19.2008296875,45.76906796875,19.3275296875Q45.80296796875,19.4541296875,45.82006796875,19.5840296875Q45.83726796875,19.714029687500002,45.83726796875,19.8451296875L45.83726796875,39.5357296875Q45.83726796875,39.6668296875,45.82006796875,39.7968296875Q45.80296796875,39.9267296875,45.76906796875,40.0533296875Q45.73516796875,40.1800296875,45.68496796875,40.3011296875Q45.63486796875,40.4222296875,45.56926796875,40.5357296875Q45.50376796875,40.6492296875,45.42396796875,40.7532296875Q45.34416796875,40.8572296875,45.25146796875,40.9499296875Q45.15876796875,41.0426296875,45.05476796875,41.1224296875Q44.95076796875,41.2022296875,44.83726796875,41.2678296875L27.78466796875,51.1130296875Q27.67116796875,51.1786296875,27.55006796875,51.2288296875Q27.42896796875,51.2789296875,27.30226796875,51.3129296875Q27.17566796875,51.3468296875,27.04576796875,51.3639296875Q26.91576796875,51.3810296875,26.78466796875,51.3810296875Q26.65356796875,51.3810296875,26.52356796875,51.3639296875Q26.39366796875,51.3468296875,26.26706796875,51.3129296875Q26.14036796875,51.2789296875,26.01926796875,51.2288296875Q25.89816796875,51.1786296875,25.78466796875,51.1130296875L8.73210796875,41.2678296875Q8.618587968749999,41.2022296875,8.51458796875,41.1224296875Q8.41058796875,41.0426296875,8.31789796875,40.9499296875Q8.22520796875,40.8572296875,8.14539796875,40.7532296875Q8.06559796875,40.6492296875,8.00005796875,40.5357296875Q7.93451796875,40.4222296875,7.88434796875,40.3011296875Q7.834187968749999,40.1800296875,7.8002579687499995,40.0533296875Q7.76632796875,39.9267296875,7.74921796875,39.7968296875Q7.73210796875,39.6668296875,7.73210796875,39.5357296875L7.73210796875,19.8451296875Q7.73210796875,19.714029687500002,7.74921796875,19.5840296875Q7.76632796875,19.4541296875,7.8002579687499995,19.3275296875Q7.834187968749999,19.2008296875,7.88434796875,19.0797296875Q7.93451796875,18.9586296875,8.00005796875,18.8451296875Q8.06559796875,18.7316296875,8.14539796875,18.6276296875Q8.22520796875,18.523629687499998,8.31789796875,18.4309296875Q8.41058796875,18.3382296875,8.51458796875,18.258429687499998Q8.618587968749999,18.1786296875,8.73210796875,18.1131296875L25.78466796875,8.2677796875Q25.89816796875,8.2022366875,26.01926796875,8.1520716875Q26.14036796875,8.1019066875,26.26706796875,8.0679786875Q26.39366796875,8.0340516875,26.52356796875,8.0169406875Q26.65356796875,7.9998306875,26.78466796875,7.9998306875Q26.91576796875,7.9998306875,27.04576796875,8.0169406875Q27.17566796875,8.0340516875,27.30226796875,8.0679786875Q27.42896796875,8.1019066875,27.55006796875,8.1520716875Q27.67116796875,8.2022366875,27.78466796875,8.2677796875L44.83726796875,18.1130296875Z"
          fill="#09173A" fill-opacity="0.8999999761581421" />
        <path
          d="M44.83726796875,18.1130296875Q44.95076796875,18.1786296875,45.05476796875,18.258429687499998Q45.15876796875,18.3382296875,45.25146796875,18.4309296875Q45.34416796875,18.523629687499998,45.42396796875,18.6276296875Q45.50376796875,18.7316296875,45.56926796875,18.8451296875Q45.63486796875,18.9586296875,45.68496796875,19.0797296875Q45.73516796875,19.2008296875,45.76906796875,19.3275296875Q45.80296796875,19.4541296875,45.82006796875,19.5840296875Q45.83726796875,19.714029687500002,45.83726796875,19.8451296875L45.83726796875,39.5357296875Q45.83726796875,39.6668296875,45.82006796875,39.7968296875Q45.80296796875,39.9267296875,45.76906796875,40.0533296875Q45.73516796875,40.1800296875,45.68496796875,40.3011296875Q45.63486796875,40.4222296875,45.56926796875,40.5357296875Q45.50376796875,40.6492296875,45.42396796875,40.7532296875Q45.34416796875,40.8572296875,45.25146796875,40.9499296875Q45.15876796875,41.0426296875,45.05476796875,41.1224296875Q44.95076796875,41.2022296875,44.83726796875,41.2678296875L27.78466796875,51.1130296875Q27.67116796875,51.1786296875,27.55006796875,51.2288296875Q27.42896796875,51.2789296875,27.30226796875,51.3129296875Q27.17566796875,51.3468296875,27.04576796875,51.3639296875Q26.91576796875,51.3810296875,26.78466796875,51.3810296875Q26.65356796875,51.3810296875,26.52356796875,51.3639296875Q26.39366796875,51.3468296875,26.26706796875,51.3129296875Q26.14036796875,51.2789296875,26.01926796875,51.2288296875Q25.89816796875,51.1786296875,25.78466796875,51.1130296875L8.73210796875,41.2678296875Q8.618587968749999,41.2022296875,8.51458796875,41.1224296875Q8.41058796875,41.0426296875,8.31789796875,40.9499296875Q8.22520796875,40.8572296875,8.14539796875,40.7532296875Q8.06559796875,40.6492296875,8.00005796875,40.5357296875Q7.93451796875,40.4222296875,7.88434796875,40.3011296875Q7.834187968749999,40.1800296875,7.8002579687499995,40.0533296875Q7.76632796875,39.9267296875,7.74921796875,39.7968296875Q7.73210796875,39.6668296875,7.73210796875,39.5357296875L7.73210796875,19.8451296875Q7.73210796875,19.714029687500002,7.74921796875,19.5840296875Q7.76632796875,19.4541296875,7.8002579687499995,19.3275296875Q7.834187968749999,19.2008296875,7.88434796875,19.0797296875Q7.93451796875,18.9586296875,8.00005796875,18.8451296875Q8.06559796875,18.7316296875,8.14539796875,18.6276296875Q8.22520796875,18.523629687499998,8.31789796875,18.4309296875Q8.41058796875,18.3382296875,8.51458796875,18.258429687499998Q8.618587968749999,18.1786296875,8.73210796875,18.1131296875L25.78466796875,8.2677796875Q25.89816796875,8.2022366875,26.01926796875,8.1520716875Q26.14036796875,8.1019066875,26.26706796875,8.0679786875Q26.39366796875,8.0340516875,26.52356796875,8.0169406875Q26.65356796875,7.9998306875,26.78466796875,7.9998306875Q26.91576796875,7.9998306875,27.04576796875,8.0169406875Q27.17566796875,8.0340516875,27.30226796875,8.0679786875Q27.42896796875,8.1019066875,27.55006796875,8.1520716875Q27.67116796875,8.2022366875,27.78466796875,8.2677796875L44.83726796875,18.1130296875Z"
          fill="url(#${lineName}l_223_11550)" fill-opacity="1" />
        <path
          d="M44.83726796875,18.1130296875Q44.95076796875,18.1786296875,45.05476796875,18.258429687499998Q45.15876796875,18.3382296875,45.25146796875,18.4309296875Q45.34416796875,18.523629687499998,45.42396796875,18.6276296875Q45.50376796875,18.7316296875,45.56926796875,18.8451296875Q45.63486796875,18.9586296875,45.68496796875,19.0797296875Q45.73516796875,19.2008296875,45.76906796875,19.3275296875Q45.80296796875,19.4541296875,45.82006796875,19.5840296875Q45.83726796875,19.714029687500002,45.83726796875,19.8451296875L45.83726796875,39.5357296875Q45.83726796875,39.6668296875,45.82006796875,39.7968296875Q45.80296796875,39.9267296875,45.76906796875,40.0533296875Q45.73516796875,40.1800296875,45.68496796875,40.3011296875Q45.63486796875,40.4222296875,45.56926796875,40.5357296875Q45.50376796875,40.6492296875,45.42396796875,40.7532296875Q45.34416796875,40.8572296875,45.25146796875,40.9499296875Q45.15876796875,41.0426296875,45.05476796875,41.1224296875Q44.95076796875,41.2022296875,44.83726796875,41.2678296875L27.78466796875,51.1130296875Q27.67116796875,51.1786296875,27.55006796875,51.2288296875Q27.42896796875,51.2789296875,27.30226796875,51.3129296875Q27.17566796875,51.3468296875,27.04576796875,51.3639296875Q26.91576796875,51.3810296875,26.78466796875,51.3810296875Q26.65356796875,51.3810296875,26.52356796875,51.3639296875Q26.39366796875,51.3468296875,26.26706796875,51.3129296875Q26.14036796875,51.2789296875,26.01926796875,51.2288296875Q25.89816796875,51.1786296875,25.78466796875,51.1130296875L8.73210796875,41.2678296875Q8.618587968749999,41.2022296875,8.51458796875,41.1224296875Q8.41058796875,41.0426296875,8.31789796875,40.9499296875Q8.22520796875,40.8572296875,8.14539796875,40.7532296875Q8.06559796875,40.6492296875,8.00005796875,40.5357296875Q7.93451796875,40.4222296875,7.88434796875,40.3011296875Q7.834187968749999,40.1800296875,7.8002579687499995,40.0533296875Q7.76632796875,39.9267296875,7.74921796875,39.7968296875Q7.73210796875,39.6668296875,7.73210796875,39.5357296875L7.73210796875,19.8451296875Q7.73210796875,19.714029687500002,7.74921796875,19.5840296875Q7.76632796875,19.4541296875,7.8002579687499995,19.3275296875Q7.834187968749999,19.2008296875,7.88434796875,19.0797296875Q7.93451796875,18.9586296875,8.00005796875,18.8451296875Q8.06559796875,18.7316296875,8.14539796875,18.6276296875Q8.22520796875,18.523629687499998,8.31789796875,18.4309296875Q8.41058796875,18.3382296875,8.51458796875,18.258429687499998Q8.618587968749999,18.1786296875,8.73210796875,18.1131296875L25.78466796875,8.2677796875Q25.89816796875,8.2022366875,26.01926796875,8.1520716875Q26.14036796875,8.1019066875,26.26706796875,8.0679786875Q26.39366796875,8.0340516875,26.52356796875,8.0169406875Q26.65356796875,7.9998306875,26.78466796875,7.9998306875Q26.91576796875,7.9998306875,27.04576796875,8.0169406875Q27.17566796875,8.0340516875,27.30226796875,8.0679786875Q27.42896796875,8.1019066875,27.55006796875,8.1520716875Q27.67116796875,8.2022366875,27.78466796875,8.2677796875L44.83726796875,18.1130296875Z"
          fill="url(#${lineName}m_223_11551)" fill-opacity="1" />
        <path
          d="M45.83726796875,19.8451296875Q45.83726796875,19.714029687500002,45.82006796875,19.5840296875Q45.80296796875,19.4541296875,45.76906796875,19.3275296875Q45.73516796875,19.2008296875,45.68496796875,19.0797296875Q45.63486796875,18.9586296875,45.56926796875,18.8451296875Q45.50376796875,18.7316296875,45.42396796875,18.6276296875Q45.34416796875,18.523629687499998,45.25146796875,18.4309296875Q45.15876796875,18.3382296875,45.05476796875,18.258429687499998Q44.95076796875,18.1786296875,44.83726796875,18.1130296875L27.78466796875,8.2677796875Q27.67116796875,8.2022366875,27.55006796875,8.1520716875Q27.42896796875,8.1019066875,27.30226796875,8.0679786875Q27.17566796875,8.0340516875,27.04576796875,8.0169406875Q26.91576796875,7.9998306875,26.78466796875,7.9998306875Q26.65356796875,7.9998306875,26.52356796875,8.0169406875Q26.39366796875,8.0340516875,26.26706796875,8.0679786875Q26.14036796875,8.1019066875,26.01926796875,8.1520716875Q25.89816796875,8.2022366875,25.78466796875,8.2677796875L8.73210796875,18.1131296875Q8.618587968749999,18.1786296875,8.51458796875,18.258429687499998Q8.41058796875,18.3382296875,8.31789796875,18.4309296875Q8.22520796875,18.523629687499998,8.14539796875,18.6276296875Q8.06559796875,18.7316296875,8.00005796875,18.8451296875Q7.93451796875,18.9586296875,7.88434796875,19.0797296875Q7.834187968749999,19.2008296875,7.8002579687499995,19.3275296875Q7.76632796875,19.4541296875,7.74921796875,19.5840296875Q7.73210796875,19.714029687500002,7.73210796875,19.8451296875L7.73210796875,39.5357296875Q7.73210796875,39.6668296875,7.74921796875,39.7968296875Q7.76632796875,39.9267296875,7.8002579687499995,40.0533296875Q7.834187968749999,40.1800296875,7.88434796875,40.3011296875Q7.93451796875,40.4222296875,8.00005796875,40.5357296875Q8.06559796875,40.6492296875,8.14539796875,40.7532296875Q8.22520796875,40.8572296875,8.31789796875,40.9499296875Q8.41058796875,41.0426296875,8.51458796875,41.1224296875Q8.618587968749999,41.2022296875,8.73210796875,41.2678296875L25.78466796875,51.1130296875Q25.89816796875,51.1786296875,26.01926796875,51.2288296875Q26.14036796875,51.2789296875,26.26706796875,51.3129296875Q26.39366796875,51.3468296875,26.52356796875,51.3639296875Q26.65356796875,51.3810296875,26.78466796875,51.3810296875Q26.91576796875,51.3810296875,27.04576796875,51.3639296875Q27.17566796875,51.3468296875,27.30226796875,51.3129296875Q27.42896796875,51.2789296875,27.55006796875,51.2288296875Q27.67116796875,51.1786296875,27.78466796875,51.1130296875L44.83726796875,41.2678296875Q44.95076796875,41.2022296875,45.05476796875,41.1224296875Q45.15876796875,41.0426296875,45.25146796875,40.9499296875Q45.34416796875,40.8572296875,45.42396796875,40.7532296875Q45.50376796875,40.6492296875,45.56926796875,40.5357296875Q45.63486796875,40.4222296875,45.68496796875,40.3011296875Q45.73516796875,40.1800296875,45.76906796875,40.0533296875Q45.80296796875,39.9267296875,45.82006796875,39.7968296875Q45.83726796875,39.6668296875,45.83726796875,39.5357296875L45.83726796875,19.8451296875ZM27.03466796875,9.5668196875L44.08726796875,19.4121296875Q44.33726796875,19.5564296875,44.33726796875,19.8451296875L44.33726796875,39.5357296875Q44.33726796875,39.8244296875,44.08726796875,39.9687296875L27.03466796875,49.8140296875Q26.78466796875,49.9584296875,26.53466796875,49.8140296875L9.48210796875,39.9687296875Q9.23210796875,39.8244296875,9.23210796875,39.5357296875L9.23210796875,19.8451296875Q9.23210796875,19.5564296875,9.48210796875,19.4121296875L26.53466796875,9.5668196875Q26.78466796875,9.422479687500001,27.03466796875,9.5668196875Z"
          fill-rule="evenodd" fill="url(#${lineName}n_223_14395)" fill-opacity="1" />
      </g>
      <g filter="url(#${lineName}o_223_09615)">
        <path
          d="M45.83726796875,19.8451296875Q45.83726796875,19.714029687500002,45.82006796875,19.5840296875Q45.80296796875,19.4541296875,45.76906796875,19.3275296875Q45.73516796875,19.2008296875,45.68496796875,19.0797296875Q45.63486796875,18.9586296875,45.56926796875,18.8451296875Q45.50376796875,18.7316296875,45.42396796875,18.6276296875Q45.34416796875,18.523629687499998,45.25146796875,18.4309296875Q45.15876796875,18.3382296875,45.05476796875,18.258429687499998Q44.95076796875,18.1786296875,44.83726796875,18.1130296875L27.78466796875,8.2677796875Q27.67116796875,8.2022366875,27.55006796875,8.1520716875Q27.42896796875,8.1019066875,27.30226796875,8.0679786875Q27.17566796875,8.0340516875,27.04576796875,8.0169406875Q26.91576796875,7.9998306875,26.78466796875,7.9998306875Q26.65356796875,7.9998306875,26.52356796875,8.0169406875Q26.39366796875,8.0340516875,26.26706796875,8.0679786875Q26.14036796875,8.1019066875,26.01926796875,8.1520716875Q25.89816796875,8.2022366875,25.78466796875,8.2677796875L8.73210796875,18.1131296875Q8.618587968749999,18.1786296875,8.51458796875,18.258429687499998Q8.41058796875,18.3382296875,8.31789796875,18.4309296875Q8.22520796875,18.523629687499998,8.14539796875,18.6276296875Q8.06559796875,18.7316296875,8.00005796875,18.8451296875Q7.93451796875,18.9586296875,7.88434796875,19.0797296875Q7.834187968749999,19.2008296875,7.8002579687499995,19.3275296875Q7.76632796875,19.4541296875,7.74921796875,19.5840296875Q7.73210796875,19.714029687500002,7.73210796875,19.8451296875L7.73210796875,39.5357296875Q7.73210796875,39.6668296875,7.74921796875,39.7968296875Q7.76632796875,39.9267296875,7.8002579687499995,40.0533296875Q7.834187968749999,40.1800296875,7.88434796875,40.3011296875Q7.93451796875,40.4222296875,8.00005796875,40.5357296875Q8.06559796875,40.6492296875,8.14539796875,40.7532296875Q8.22520796875,40.8572296875,8.31789796875,40.9499296875Q8.41058796875,41.0426296875,8.51458796875,41.1224296875Q8.618587968749999,41.2022296875,8.73210796875,41.2678296875L25.78466796875,51.1130296875Q25.89816796875,51.1786296875,26.01926796875,51.2288296875Q26.14036796875,51.2789296875,26.26706796875,51.3129296875Q26.39366796875,51.3468296875,26.52356796875,51.3639296875Q26.65356796875,51.3810296875,26.78466796875,51.3810296875Q26.91576796875,51.3810296875,27.04576796875,51.3639296875Q27.17566796875,51.3468296875,27.30226796875,51.3129296875Q27.42896796875,51.2789296875,27.55006796875,51.2288296875Q27.67116796875,51.1786296875,27.78466796875,51.1130296875L44.83726796875,41.2678296875Q44.95076796875,41.2022296875,45.05476796875,41.1224296875Q45.15876796875,41.0426296875,45.25146796875,40.9499296875Q45.34416796875,40.8572296875,45.42396796875,40.7532296875Q45.50376796875,40.6492296875,45.56926796875,40.5357296875Q45.63486796875,40.4222296875,45.68496796875,40.3011296875Q45.73516796875,40.1800296875,45.76906796875,40.0533296875Q45.80296796875,39.9267296875,45.82006796875,39.7968296875Q45.83726796875,39.6668296875,45.83726796875,39.5357296875L45.83726796875,19.8451296875ZM27.03466796875,9.5668196875L44.08726796875,19.4121296875Q44.33726796875,19.5564296875,44.33726796875,19.8451296875L44.33726796875,39.5357296875Q44.33726796875,39.8244296875,44.08726796875,39.9687296875L27.03466796875,49.8140296875Q26.78466796875,49.9584296875,26.53466796875,49.8140296875L9.48210796875,39.9687296875Q9.23210796875,39.8244296875,9.23210796875,39.5357296875L9.23210796875,19.8451296875Q9.23210796875,19.5564296875,9.48210796875,19.4121296875L26.53466796875,9.5668196875Q26.78466796875,9.422479687500001,27.03466796875,9.5668196875Z"
          fill-rule="evenodd" fill="url(#${lineName}p_223_14383)" fill-opacity="1" />
      </g>
    </g>
  </g>
</svg>`;
};

const getBoxColorVars = (color: string) => {
  const colorDto = Color(color);
  const colorDarkHax = colorDto.darken(0.2).hex();
  const colorPureHax = colorDto.lighten(0.92).hex();
  const colorLight1Hax = colorDto.lighten(0.3).hex();
  const colorLight2Hax = colorDto.lighten(0.75).hex();
  const colorLight3Hax = colorDto.lighten(0.83).hex();
  return `--color: ${color};
    --color-dark: ${colorDarkHax};
    --color-pure: ${colorPureHax};
    --color-light1: ${colorLight1Hax};
    --color-light2: ${colorLight2Hax};
    --color-light3: ${colorLight3Hax};`;
};

export const EVENT_MARKER_WIDTH = 52;
export const EVENT_MARKER_HEIGHT = 104;

export function getEventMarker(color: string, reasonIcon: string, scale = 1) {
  if (scale === 0) {
    return divIcon({
      html: ``,
      iconSize: [0, 0],
    });
  }
  const width = EVENT_MARKER_WIDTH;
  const height = EVENT_MARKER_HEIGHT;

  const eventBoxSvg = getEventBoxSvg(color, scale);

  return divIcon({
    html: `
       <div class="relative" style="${getBoxColorVars(color)}">
          ${eventBoxSvg}
          <div class="absolute w-full left-0 flex-center text-white" style="top: 17.5%">
            <img style="width: ${24 * scale}px;" src="${reasonIcon}" alt="" />
          </div>
        </div>
    `,
    className: "active-effect",
    iconSize: [width * scale, height * scale],
    iconAnchor: [(width * scale) / 2, 0.91 * height * scale],
  });
}

export function getEventPopUpItem(
  color: string,
  reasonIcon: string,
  title: string,
  _scale: number
) {
  return `
  <div class="relative mt-[2px]" style="${getBoxColorVars(color)}">
    <svg class="align-bottom!" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none"
    version="1.1" width="300" height="44" viewBox="0 0 300 44">
    <defs>
      <filter id="${color}-0_223_09618" filterUnits="objectBoundingBox"
        color-interpolation-filters="sRGB" x="0" y="0" width="1" height="1">
        <feFlood flood-opacity="0" result="BackgroundImageFix" />
        <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
        <feColorMatrix in="SourceAlpha" type="matrix" result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" />
        <feOffset dy="0" dx="0" />
        <feGaussianBlur stdDeviation="4" />
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
        <feColorMatrix type="matrix"
          values="0 0 0 0 0.9019607901573181 0 0 0 0 0 0 0 0 0 0.16470588743686676 0 0 0 0.23999999463558197 0" />
        <feBlend mode="normal" in2="shape" result="effect1_innerShadow" />
      </filter>
      <linearGradient x1="0.5" y1="0" x2="0.5" y2="1" id="${color}-1_223_11474">
        <stop offset="0%" stop-color="var(--color)" stop-opacity="0.10000000149011612" />
        <stop offset="100%" stop-color="var(--color)" stop-opacity="0.30000001192092896" />
      </linearGradient>
      <radialGradient cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" id="${color}-2_223_11475"
        gradientTransform="translate(150 38.59445309638977) rotate(90) scale(27.47758984565735 301.38520752290884)">
        <stop offset="0%" stop-color="var(--color)" stop-opacity="0.20000000298023224" />
        <stop offset="100%" stop-color="var(--color)" stop-opacity="0" />
      </radialGradient>
      <linearGradient x1="0.004829778335988522" y1="0" x2="1.0211406580538331"
        y2="0.021968077200384073" id="${color}-3_223_11439">
        <stop offset="0%" stop-color="var(--color)" stop-opacity="1" />
        <stop offset="19.636431336402893%" stop-color="var(--color)" stop-opacity="0.30000001192092896" />
        <stop offset="71.01495862007141%" stop-color="var(--color)" stop-opacity="0.30000001192092896" />
        <stop offset="100%" stop-color="var(--color)" stop-opacity="1" />
      </linearGradient>
      <linearGradient x1="0.5" y1="0" x2="0.5" y2="1" id="${color}-4_223_11550">
        <stop offset="0%" stop-color="var(--color)" stop-opacity="0.30000001192092896" />
        <stop offset="98.57142567634583%" stop-color="var(--color)" stop-opacity="0.5" />
      </linearGradient>
      <radialGradient cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" id="${color}-5_223_11551"
        gradientTransform="translate(22 38.485759973526) rotate(90) scale(24.58578658103943 33.109551608373096)">
        <stop offset="0%" stop-color="var(--color)" stop-opacity="1" />
        <stop offset="100%" stop-color="var(--color)" stop-opacity="0" />
      </radialGradient>
      <linearGradient x1="0.5" y1="0" x2="0.5" y2="1" id="${color}-6_223_11519">
        <stop offset="0%" stop-color="var(--color)" stop-opacity="1" />
        <stop offset="100%" stop-color="#FFFFFF" stop-opacity="0.6000000238418579" />
      </linearGradient>
      <linearGradient x1="0.023781754076480865" y1="0.5" x2="0.9959614276885986" y2="0.5"
        id="${color}-7_223_11483">
        <stop offset="8.59377533197403%" stop-color="var(--color)" stop-opacity="0" />
        <stop offset="100%" stop-color="var(--color)" stop-opacity="0.30000001192092896" />
      </linearGradient>
    </defs>
    <g>
      <g filter="url(#${color}-0_223_09618)">
        <path
          d="M0,7.82843L0,36.1716C0,36.702,0.210714,37.2107,0.585786,37.5858L6.41421,43.4142C6.78929,43.7893,7.29799,44,7.82843,44L292.172,44C292.702,44,293.211,43.7893,293.586,43.4142L299.414,37.5858C299.789,37.2107,300,36.702,300,36.1716L300,7.82843C300,7.29799,299.789,6.78929,299.414,6.41421L293.586,0.585786C293.211,0.210714,292.702,0,292.172,0L7.82843,0C7.29799,0,6.78929,0.210714,6.41421,0.585786L0.585786,6.41421C0.210714,6.78929,0,7.29799,0,7.82843Z"
          fill="#09132E" fill-opacity="0.800000011920929" />
        <path
          d="M0,7.82843L0,36.1716C0,36.702,0.210714,37.2107,0.585786,37.5858L6.41421,43.4142C6.78929,43.7893,7.29799,44,7.82843,44L292.172,44C292.702,44,293.211,43.7893,293.586,43.4142L299.414,37.5858C299.789,37.2107,300,36.702,300,36.1716L300,7.82843C300,7.29799,299.789,6.78929,299.414,6.41421L293.586,0.585786C293.211,0.210714,292.702,0,292.172,0L7.82843,0C7.29799,0,6.78929,0.210714,6.41421,0.585786L0.585786,6.41421C0.210714,6.78929,0,7.29799,0,7.82843Z"
          fill="url(#${color}-1_223_11474)" fill-opacity="1" />
        <path
          d="M0,7.82843L0,36.1716C0,36.702,0.210714,37.2107,0.585786,37.5858L6.41421,43.4142C6.78929,43.7893,7.29799,44,7.82843,44L292.172,44C292.702,44,293.211,43.7893,293.586,43.4142L299.414,37.5858C299.789,37.2107,300,36.702,300,36.1716L300,7.82843C300,7.29799,299.789,6.78929,299.414,6.41421L293.586,0.585786C293.211,0.210714,292.702,0,292.172,0L7.82843,0C7.29799,0,6.78929,0.210714,6.41421,0.585786L0.585786,6.41421C0.210714,6.78929,0,7.29799,0,7.82843Z"
          fill="url(#${color}-2_223_11475)" fill-opacity="1" />
        <path
          d="M0,7.82843L0,36.1716C0,36.702,0.210714,37.2107,0.585786,37.5858L6.41421,43.4142C6.78929,43.7893,7.29799,44,7.82843,44L292.172,44C292.702,44,293.211,43.7893,293.586,43.4142L299.414,37.5858C299.789,37.2107,300,36.702,300,36.1716L300,7.82843C300,7.29799,299.789,6.78929,299.414,6.41421L293.586,0.585786C293.211,0.210714,292.702,0,292.172,0L7.82843,0C7.29799,0,6.78929,0.210714,6.41421,0.585786L0.585786,6.41421C0.210714,6.78929,0,7.29799,0,7.82843ZM1,7.82843L1,36.1716Q1,36.5858,1.29289,36.8787L7.12132,42.7071Q7.41421,43,7.82843,43L292.172,43Q292.586,43,292.879,42.7071L298.707,36.8787Q299,36.5858,299,36.1716L299,7.82843Q299,7.41423,298.707,7.12129L292.879,1.29289Q292.586,1,292.172,1L7.82843,1Q7.41421,1,7.12132,1.29289L1.29289,7.12132Q1,7.41421,1,7.82843Z"
          fill-rule="evenodd" fill="url(#${color}-3_223_11439)" fill-opacity="1" />
      </g>
      <g>
        <path
          d="M4,33.1716C4,33.702,4.210714,34.2107,4.585786,34.5858L9.41421,39.4142C9.789290000000001,39.7893,10.29799,40,10.82843,40L33.1716,40C33.702,40,34.2107,39.7893,34.5858,39.4142L39.4142,34.5858C39.7893,34.2107,40,33.702,40,33.1716L40,10.82843C40,10.29799,39.7893,9.789290000000001,39.4142,9.41421L34.5858,4.585786C34.2107,4.210714,33.702,4,33.1716,4L10.82843,4C10.29799,4,9.789290000000001,4.210714,9.41421,4.585786L4.585786,9.41421C4.210714,9.789290000000001,4,10.29799,4,10.82843L4,33.1716Z"
          fill="#09173A" fill-opacity="0.8999999761581421" />
        <path
          d="M4,33.1716C4,33.702,4.210714,34.2107,4.585786,34.5858L9.41421,39.4142C9.789290000000001,39.7893,10.29799,40,10.82843,40L33.1716,40C33.702,40,34.2107,39.7893,34.5858,39.4142L39.4142,34.5858C39.7893,34.2107,40,33.702,40,33.1716L40,10.82843C40,10.29799,39.7893,9.789290000000001,39.4142,9.41421L34.5858,4.585786C34.2107,4.210714,33.702,4,33.1716,4L10.82843,4C10.29799,4,9.789290000000001,4.210714,9.41421,4.585786L4.585786,9.41421C4.210714,9.789290000000001,4,10.29799,4,10.82843L4,33.1716Z"
          fill="url(#${color}-4_223_11550)" fill-opacity="1" />
        <path
          d="M4,33.1716C4,33.702,4.210714,34.2107,4.585786,34.5858L9.41421,39.4142C9.789290000000001,39.7893,10.29799,40,10.82843,40L33.1716,40C33.702,40,34.2107,39.7893,34.5858,39.4142L39.4142,34.5858C39.7893,34.2107,40,33.702,40,33.1716L40,10.82843C40,10.29799,39.7893,9.789290000000001,39.4142,9.41421L34.5858,4.585786C34.2107,4.210714,33.702,4,33.1716,4L10.82843,4C10.29799,4,9.789290000000001,4.210714,9.41421,4.585786L4.585786,9.41421C4.210714,9.789290000000001,4,10.29799,4,10.82843L4,33.1716Z"
          fill="url(#${color}-5_223_11551)" fill-opacity="1" />
        <path
          d="M4,33.1716C4,33.702,4.210714,34.2107,4.585786,34.5858L9.41421,39.4142C9.789290000000001,39.7893,10.29799,40,10.82843,40L33.1716,40C33.702,40,34.2107,39.7893,34.5858,39.4142L39.4142,34.5858C39.7893,34.2107,40,33.702,40,33.1716L40,10.82843C40,10.29799,39.7893,9.789290000000001,39.4142,9.41421L34.5858,4.585786C34.2107,4.210714,33.702,4,33.1716,4L10.82843,4C10.29799,4,9.789290000000001,4.210714,9.41421,4.585786L4.585786,9.41421C4.210714,9.789290000000001,4,10.29799,4,10.82843L4,33.1716ZM5.5,33.1716Q5.5,33.378699999999995,5.64645,33.525099999999995L10.47487,38.3536Q10.62132,38.5,10.82843,38.5L33.1716,38.5Q33.378699999999995,38.5,33.525099999999995,38.3536L38.3536,33.525099999999995Q38.5,33.378699999999995,38.5,33.1716L38.5,10.82843Q38.5,10.62132,38.3536,10.47487L33.525099999999995,5.64645Q33.378699999999995,5.5,33.1716,5.5L10.82843,5.5Q10.62132,5.5,10.47487,5.64645L5.64645,10.47487Q5.5,10.62132,5.5,10.82843L5.5,33.1716Z"
          fill-rule="evenodd" fill="url(#${color}-6_223_11519)" fill-opacity="0.5" />
      </g>
      <g>
        <path
          d="M289.793,39.5L34,39.5L34,40.5L290.207,40.5L296.5,34.2071L296.5,9.79289L290.207,3.5L34,3.5L34,4.5L289.793,4.5L295.5,10.2071L295.5,33.7929L289.793,39.5Z"
          fill-rule="evenodd" fill="url(#${color}-7_223_11483)" fill-opacity="1" />
      </g>
    </g>
  </svg>
  <div class="absolute w-full h-full left-0 top-0 flex text-white">
      <div class="flex-center" style="width: 14%">
        <img width="${24}" src="${reasonIcon}" alt="" />
      </div>
      <div class="flex-1 event-popup-title-font px-2 line-clamp-1" style="line-height: 44px;">
        <span>${title}${title}${title}${title}${title}${title}${title}</span>
      </div>
  </div>
  </div>
`;
}

interface EventLike {
  reason: string;
  profession: string;
  delay: string;
  time: string;
}

export function getEventPopUpDetail(color: string, ev: EventLike) {
  const getFiledDom = ({ label, value }: { label: string; value: string }) => {
    return `<div style="font-size: 14px">
      <span style="color: rgba(255, 255, 255, 0.85)">${label}：</span>
      <span>${value}</span>
    </div>`;
  };
  const fields = [
    { label: "原因", value: ev.reason },
    { label: "专业", value: ev.profession },
    { label: "晚点", value: ev.delay },
    { label: "时间", value: ev.time },
  ]
    .map((f) => getFiledDom(f))
    .join("");

  return `
    <div class="relative mt-[1px]" style="${getBoxColorVars(color)}">
    <svg class="align-bottom!" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none"
      version="1.1" width="300" height="65" viewBox="0 0 300 65">
      <defs>
        <filter id="${color}-0_223_09617" filterUnits="objectBoundingBox"
          color-interpolation-filters="sRGB" x="0" y="0" width="1" height="1">
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
          <feColorMatrix in="SourceAlpha" type="matrix" result="hardAlpha"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" />
          <feOffset dy="0" dx="0" />
          <feGaussianBlur stdDeviation="4" />
          <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
          <feColorMatrix type="matrix"
            values="0 0 0 0 0.9019607901573181 0 0 0 0 0 0 0 0 0 0.16470588743686676 0 0 0 0.1599999964237213 0" />
          <feBlend mode="normal" in2="shape" result="effect1_innerShadow" />
        </filter>
        <linearGradient x1="0.5" y1="0" x2="0.5" y2="1" id="${color}-1_223_11511">
          <stop offset="0%" stop-color="var(--color)" stop-opacity="0.10000000149011612" />
          <stop offset="100%" stop-color="var(--color)" stop-opacity="0.20000000298023224" />
        </linearGradient>
        <linearGradient x1="0.004829778335988522" y1="0" x2="1.0448579807489067"
          y2="0.049060497496792976" id="${color}-2_223_11499">
          <stop offset="0%" stop-color="var(--color)" stop-opacity="0.6000000238418579" />
          <stop offset="19.636431336402893%" stop-color="var(--color)" stop-opacity="0.30000001192092896" />
          <stop offset="71.01495862007141%" stop-color="var(--color)" stop-opacity="0.30000001192092896" />
          <stop offset="100%" stop-color="var(--color)" stop-opacity="0.6000000238418579" />
        </linearGradient>
      </defs>
      <g>
        <g filter="url(#${color}-0_223_09617)">
          <path
            d="M0,57.1716C0,57.702,0.210714,58.2107,0.585786,58.5858L6.41421,64.4142C6.78929,64.7893,7.29799,65,7.82843,65L292.172,65C292.702,65,293.211,64.7893,293.586,64.4142L299.414,58.5858C299.789,58.2107,300,57.702,300,57.1716L300,7.41421C300,7.149,299.895,6.89464,299.707,6.70711L293.586,0.585786C293.211,0.210714,292.702,0,292.172,0L7.82843,0C7.29799,0,6.78929,0.210714,6.41421,0.585786L0.292893,6.70711C0.105357,6.89464,0,7.149,0,7.41421L0,57.1716Z"
            fill="#09132E" fill-opacity="0.800000011920929" />
          <path
            d="M0,57.1716C0,57.702,0.210714,58.2107,0.585786,58.5858L6.41421,64.4142C6.78929,64.7893,7.29799,65,7.82843,65L292.172,65C292.702,65,293.211,64.7893,293.586,64.4142L299.414,58.5858C299.789,58.2107,300,57.702,300,57.1716L300,7.41421C300,7.149,299.895,6.89464,299.707,6.70711L293.586,0.585786C293.211,0.210714,292.702,0,292.172,0L7.82843,0C7.29799,0,6.78929,0.210714,6.41421,0.585786L0.292893,6.70711C0.105357,6.89464,0,7.149,0,7.41421L0,57.1716Z"
            fill="url(#${color}-1_223_11511)" fill-opacity="1" />
          <path
            d="M0,57.1716C0,57.702,0.210714,58.2107,0.585786,58.5858L6.41421,64.4142C6.78929,64.7893,7.29799,65,7.82843,65L292.172,65C292.702,65,293.211,64.7893,293.586,64.4142L299.414,58.5858C299.789,58.2107,300,57.702,300,57.1716L300,7.41421C300,7.149,299.895,6.89464,299.707,6.70711L293.586,0.585786C293.211,0.210714,292.702,0,292.172,0L7.82843,0C7.29799,0,6.78929,0.210714,6.41421,0.585786L0.292893,6.70711C0.105357,6.89464,0,7.149,0,7.41421L0,57.1716ZM1,57.1716Q1,57.5858,1.29289,57.8787L7.12132,63.7071Q7.41421,64,7.82843,64L292.172,64Q292.586,64,292.879,63.7071L298.707,57.8787Q299,57.5858,299,57.1716L299,7.41421L292.879,1.29289Q292.586,1,292.172,1L7.82843,1Q7.41421,1,7.12132,1.29289L1,7.41421L1,57.1716Z"
            fill-rule="evenodd" fill="url(#${color}-2_223_11499)" fill-opacity="0.5" />
        </g>
      </g>
    </svg>
    <div class="absolute w-full h-full left-0 top-0 flex text-white">
      <div class="w-full grid grid-cols-2 leading-6 px-2 py-2">
        ${fields}
      </div>
    </div>
    </div>
  `;
}
