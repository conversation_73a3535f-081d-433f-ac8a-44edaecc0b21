import DarkModeSvg from "@/assets/images/portal/map/dark-mode.svg?react";
import { eventEffectService } from "@/services/event.marker.service";
import { lineEffectService } from "@/services/line.marker.service";
import { mapService } from "@/services/map.service";
import { linesData2025 } from "@/utils/map/meta";
import { MetroLine } from "@/utils/map/metro.line.class";
import { useMount, useUpdateEffect } from "ahooks";
import { clsx } from "clsx";
import { LayerGroup, Map as LeafletMap } from "leaflet";
import { useCallback, useRef } from "react";
import PortalFilter from "./Filter";
import HeatButton from "./HeatButton";
import { LineSelectOverlay } from "./LineSelectOverlay";
import PortalPanel from "./Panel";
import styles from "./portal.map.module.scss";
import useMapState from "./useMapState";

const lineLayerGroup: LayerGroup = new LayerGroup([]);
const stationLayerGroup: LayerGroup = new LayerGroup([]);

async function initMetro(lineModels: MetroLine[], map: LeafletMap) {
  if (!lineModels.length) {
    const lines = linesData2025;
    lineModels = lines.map((lineData) => {
      return new MetroLine({ meta: lineData });
    });
    stationLayerGroup.setZIndex(800);
    lineLayerGroup.setZIndex(100);
    lineModels.forEach((l) =>
      l.mount([lineLayerGroup, stationLayerGroup], map)
    );
    lineEffectService.mount(map, lineModels);
    eventEffectService.mount(map, lineModels);
    eventEffectService.initializeLineEvents();
  }
}

function onHeatMapVisibleChange(visible: boolean) {
  if (visible) {
    mapService.showHeatMap([
      ...linesData2025[0].points.map(
        (p) => [p.coord[0], p.coord[1], 0.85] as [number, number, number]
      ),
      ...linesData2025[3].points.map(
        (p) => [p.coord[0], p.coord[1], 0.7] as [number, number, number]
      ),
    ]);
    // eventEffectService.setVisibility(false);
  } else {
    mapService.removeHeatMap();
    // eventEffectService.setVisibility(true);
  }
}

function PortalMap() {
  const mapDomRef = useRef<HTMLDivElement>(null);
  const mapRef = useRef<LeafletMap | null>(null);
  const lineModels = useRef<MetroLine[]>([]);

  const {
    cases,
    filterValues,
    setFilterValues,
    categoryOptions,
    updateCategoryLabel,
  } = useMapState();

  const filterProps = {
    values: filterValues,
    onChange: setFilterValues,
    categoryOptions,
    updateCategoryLabel,
  };

  useUpdateEffect(() => {
    eventEffectService.diffEventsAndEffect(cases || []);
  }, [cases]);

  useMount(() => {
    const map = mapService.getMap(mapDomRef.current!);
    mapRef.current = map;
    lineLayerGroup.addTo(map);
    stationLayerGroup.addTo(map);
    initMetro(lineModels.current, map);
  });

  const onLineChange = useCallback(
    (lines: string[]) => {
      lineEffectService.followVisibility(lines);
      setFilterValues({ ...filterValues, lines });
    },
    [setFilterValues]
  );

  return (
    <div className={styles.portalMap}>
      <DarkModeSvg />
      <div className={styles.mapContainer}>
        <div
          className={clsx(styles.map, "dark-mode", "map-dark-bg")}
          ref={mapDomRef}
        ></div>
      </div>
      <PortalFilter {...filterProps} />
      <PortalPanel {...filterProps} events={cases || []} />
      <HeatButton
        className={styles.heatButtonPos}
        onClick={onHeatMapVisibleChange}
      />
      <LineSelectOverlay onChange={onLineChange} />
    </div>
  );
}

export default PortalMap;
