@mixin scrollbar-style {
  &::-webkit-scrollbar {
    width: 6px;
    background: #1f375b;
  }
  &::-webkit-scrollbar-thumb {
    background: #0c4d87;
    border-radius: 3px;
  }
  &::-webkit-scrollbar-track {
    background: #1f375b;
  }
}

.tree {
  width: 260px;
  height: 100%;
  overflow-y: auto;
  @include scrollbar-style;
  .trees {
    width: 100%;
    height: 100%;
    background: rgba(25, 42, 88, 0.2);
    color: rgba(255, 255, 255, 0.75);
  }
}
.table {
  width: calc(100% - 280px);
  height: 100%;
  .header {
    width: 100%;
    height: 80px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .tableBody {
    width: 100%;
    height: calc(100% - 80px);
    .statusOne {
      border-radius: 8px 8px 8px 8px;
      border: 2px solid
        rgba(251.96428209543228, 166.7869734764099, 39.02101814746857, 1);
      background-color: #fca7273d;
    }
    .statusTwo {
      width: 104px;
      border-radius: 8px 8px 8px 8px;
      border: 2px solid
        rgba(251.96428209543228, 39.02101814746857, 39.02101814746857, 1);
      background-color: #fc272744;
    }
    .statusThree {
      width: 72px;
      border-radius: 8px 8px 8px 8px;
      border: 2px solid
        rgba(202.58020162582397, 210.98214358091354, 209.58182752132416, 1);
      background-color: #cbd3d234;
    }
    .statusFour {
      width: 72px;
      height: 28px;
      border-radius: 8px 8px 8px 8px;
      border: 2px solid rgba(47.38938130438328, 255, 220.3982511162758, 1);
      background-color: #2fffdc38;
    }
  }
  .searchWrapper {
    position: relative;
    display: flex;
    align-items: center;
    .searchInput {
      width: 300px;
      height: 35px;
      background: rgba(9, 27, 74, 0.4) !important;
      border-radius: 4px 4px 4px 4px;
      border: 1px solid rgba(92, 178, 254, 0.4);
      &:focus {
        outline: none;
      }
      padding-left: 8px;
      color: #ffffff;
    }
    .searchButtonInside {
      position: absolute;
      right: 0;
      height: 35px;
      background-color: transparent;
      border: none;
      color: #ffffff;
      font-size: 24px;
      padding: 0 8px;
      cursor: pointer;
    }
  }
  .even-row {
    margin-top: 2px;
    background: rgba(60, 126, 255, 0.06);
    box-shadow: inset 0px 0px 24px 0px rgba(47, 116, 255, 0.15);
    border-radius: 0px 0px 0px 0px;
    border: 1px solid;
    border-image: linear-gradient(
        270deg,
        rgba(41.00000135600567, 200.00000327825546, 255, 0),
        rgba(41.00000135600567, 200.00000327825546, 255, 0.30000001192092896),
        rgba(41.00000135600567, 200.00000327825546, 255, 0)
      )
      1 1;
  }
  .odd-row {
    background: rgba(60, 126, 255, 0.06);
    box-shadow: inset 0px 0px 24px 0px rgba(47, 116, 255, 0.15);
    border-radius: 0px 0px 0px 0px;
    border: 1px solid;
    border-image: linear-gradient(
        270deg,
        rgba(41.00000135600567, 200.00000327825546, 255, 0),
        rgba(41.00000135600567, 200.00000327825546, 255, 0.30000001192092896),
        rgba(41.00000135600567, 200.00000327825546, 255, 0)
      )
      1 1;
  }
  .importButton {
    background: transparent;
    width: 84px;
    height: 36px;
    color: #ffffff;
    background: radial-gradient(
        100% 100% at 0% 88%,
        rgba(41, 196, 251, 0.7) 0%,
        rgba(41, 196, 251, 0) 100%
      ),
      rgba(60, 126, 255, 0.3);
    border-radius: 4px;
    border: 1px solid rgba(42, 138, 255, 0.5);
    &:hover,
    &:active,
    &:focus {
      background: radial-gradient(
          100% 100% at 0% 88%,
          rgba(41, 195, 251, 0.63) 0%,
          rgba(41, 195, 251, 0.103) 100%
        ),
        rgba(60, 126, 255, 0.3) !important;
      border: 1px solid rgba(42, 138, 255, 0.692) !important;
    }
  }
}
