import clsx from "clsx";
import { useState } from "react";
import { FilterState, _categoryOptions } from "../useMapState";
import styles from "./portal.filter.module.scss";

import ArrowIcon from "@/assets/images/portal/map/filter/arrow-icon.svg?react";

interface Props {
  values: FilterState;
  onChange: (values: FilterState) => void;
  categoryOptions: typeof _categoryOptions;
  updateCategoryLabel: (categoryKey: string, key: string | number) => void;
}

const PortalFilter = (p: Props) => {
  const [openedCategory, setOpenedCategory] = useState("reason");

  function isSubOptionActive(category: string, key: string | number) {
    switch (category) {
      case "reason":
        return p.values.reason === key;
      case "delay":
        return p.values.delay === key;
      case "dateType":
        return p.values.dateType === key;
      case "durationType":
        return p.values.durationType === key;
      default:
        return false;
    }
  }

  function onCategoryClick(key: string) {
    if (openedCategory === key) {
      setOpenedCategory("");
      return;
    }
    setOpenedCategory(key);
  }

  function onSubOptionClick(option: Option) {
    switch (openedCategory) {
      case "reason":
        p.onChange({ ...p.values, reason: option.label });
        break;
      case "delay":
        p.onChange({ ...p.values, delay: option.value as string });
        break;
      case "dateType":
        p.onChange({ ...p.values, dateType: option.value as string });
        break;
      case "durationType":
        p.onChange({ ...p.values, durationType: option.value as string });
        break;
      default:
        break;
    }
    p.updateCategoryLabel(openedCategory, option.value);
  }

  return (
    <div className={styles.portalFilter}>
      {p.categoryOptions.map((category) => {
        return (
          <div className={styles.categoryItem} key={category.key}>
            <div
              className={clsx(styles.categoryButton, {
                [styles.active]: openedCategory === category.key,
              })}
              onClick={() => onCategoryClick(category.key)}
            >
              <div className={styles.iconContainer}>
                <img src={category.icon} alt="" />
              </div>
              <div className={styles.label}>{category.label}</div>
              <div>
                <ArrowIcon
                  className={clsx(
                    {
                      "-rotate-180": openedCategory === category.key,
                    },
                    "transition-transform duration-300 ml-2"
                  )}
                />
              </div>
            </div>
            <div
              className={styles.subOptions}
              style={{
                height:
                  openedCategory === category.key
                    ? category.children.length * 46 + 2 + "px"
                    : 0,
              }}
            >
              {category.children.map((item) => {
                return (
                  <div
                    key={item.value}
                    className={clsx(styles.subOption, {
                      [styles.active]: isSubOptionActive(
                        category.key,
                        item.value
                      ),
                    })}
                    onClick={() => onSubOptionClick(item)}
                  >
                    <div className={styles.label}>
                      <span className={styles.dot}></span>
                      <span>{item.label}</span>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default PortalFilter;
