import { ThemeConfig } from "antd";
import { theme } from "antd/es";

// 深色
export const darkTheme: ThemeConfig = {
  token: {
    colorBgBase: "#040a1e",
    colorTextBase: "#eaf9ff",
  },
  algorithm: theme.darkAlgorithm,
  components: {
    Select: {
      colorIcon: "#fff",
      selectorBg: "rgba(5,21,33,0.9) rgba(28,54,128,0.2);",
      colorText: "#fff",
      colorBorder: "rgba(52, 162, 254, 0.25)",
      colorBgElevated: "#051521",
      controlItemBgHover: "#40a9ff",
      controlItemBgActive: "rgba(92, 178, 254, 0.4)",
      colorTextHeading: "#fff",
      colorTextLabel: "#fff",
      colorPrimaryHover: "#40a9ff",
    },
    Button: {
      defaultBg: "none",
      defaultBorderColor: "none",
      defaultActiveBg: "none",
      defaultHoverBg: "none",
      defaultHoverBorderColor: "none",
      defaultColor: "rgba(223,234,255,0.8);",
      defaultHoverColor: "#fff ",
      defaultActiveColor: "rgba(223,234,255,0.8); ",
    },
    Tree: {
      nodeSelectedColor: "#29CAFF",
      nodeSelectedBg: "rgba(41,202,255,0.2)",
      titleHeight: 40,
      indentSize: 40,
    },
    Table: {
      headerColor: "#DFEAFF",
      rowHoverBg: "rgba(60,126,255,0.2)",
      headerBg:
        "linear-gradient( 180deg, rgba(116,163,255,0.15) 0%, rgba(116,163,255,0.05) 100%);",
    },
  },
};

// 浅色
export const lightTheme: ThemeConfig = {
  token: {
    colorBgBase: "#ffffff",
    colorTextBase: "#000000",
  },
  algorithm: theme.defaultAlgorithm,
};

// 主题Hook
export function useTheme() {
  return {
    darkTheme,
    lightTheme,
  };
}
