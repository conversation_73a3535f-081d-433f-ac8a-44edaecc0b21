import { darkTheme } from "@/hooks/useTheme";
import { SearchOutlined } from "@ant-design/icons";
import { ConfigProvider, Layout, message } from "antd";
import React, { useCallback, useEffect, useState } from "react";
import { IMAGES } from "../Traffic/images";
import CaseDetail from "./components/CaseDetail";
import { CustomSelect } from "./CustomSelect";
import styles from "./index.module.scss";
interface EventItem {
  id: number;
  title: string;
  level: string;
  nature: string;
  line: string;
  dateFeature: string;
  reason: string;
  delay: string;
  handling: string;
}

// 事件列表项组件
const EventListItem = React.memo(
  ({
    event,
    selected,
    onClick,
  }: {
    event: EventItem;
    selected: boolean;
    onClick: () => void;
  }) => (
    <div
      key={event.id}
      className={`${styles.list} ${selected ? styles.selected : ""}`}
      onClick={onClick}
    >
      <div className={styles.contentTitleText}>{event.title}</div>
      <div className={styles.contentTextSan}>
        <div className={styles.contentTextLeft}>
          <img src={IMAGES.cart} alt="" />
        </div>
        <div className="w-[65%] pl-4">
          <div className={styles.contentTextTitle}>
            <button className={styles.soButton}>{event.level}</button>
            <button className={styles.nature}>{event.nature}</button>
          </div>
          <div className={styles.contentTextContent}>
            <div className={styles.contentTextP}>
              所属线路：
              <span className={styles.contentTextSpan}>{event.line}</span>
            </div>
            <div className={styles.contentTextP}>
              日期特征：
              <span className={styles.contentTextSpan}>
                {event.dateFeature}
              </span>
            </div>
            <div className={styles.contentTextP}>
              事件原因：
              <span className={styles.contentTextSpan}>{event.reason}</span>
            </div>
            <div className={styles.contentTextP}>
              最大晚点：
              <span className={styles.contentTextSpan}>{event.delay}</span>
            </div>
          </div>
        </div>
      </div>
      <div
        className={styles.topRight}
        style={{
          backgroundImage:
            event.handling === "处置不足"
              ? `url(${IMAGES.insufficientDisposal})`
              : event.handling === "处置充分"
              ? `url(${IMAGES.outstanding})`
              : `url(${IMAGES.noImg})`,
        }}
      >
        {event.handling === "处置不足" && <img src={IMAGES.insufficient} />}
        {event.handling === "处置充分" && <img src={IMAGES.outstandingIcon} />}
        <span className="pb-0.5 px-1">{event.handling}</span>
      </div>
    </div>
  )
);

const Contrast: React.FC = () => {
  const [selectedEventIds, setSelectedEventIds] = useState<number[]>([]);
  const { Content } = Layout;
  const [isCaseDetail, setIsCaseDetail] = useState<boolean>(false);
  const [select1Open, setSelect1Open] = useState<boolean>(false);
  const [select2Open, setSelect2Open] = useState<boolean>(false);
  const [select3Open, setSelect3Open] = useState<boolean>(false);
  const [select4Open, setSelect4Open] = useState<boolean>(false);
  const [select5Open, setSelect5Open] = useState<boolean>(false);
  const [select6Open, setSelect6Open] = useState<boolean>(false);

  const [addEventData, setAddEventData] = useState<EventItem[]>([]);
  const [messageApi, contextHolder] = message.useMessage();
  // 模拟事件数据
  const eventData: EventItem[] = [
    {
      id: 1,
      title: "0504车辆故障救援事件",
      level: "一般A类事故",
      nature: "自然灾害类",
      line: "浦江线",
      dateFeature: "工作日",
      reason: "车辆故障",
      delay: "15分钟以上",
      handling: "处置不足",
    },
    {
      id: 2,
      title: "0612信号系统故障",
      level: "一般B类事故",
      nature: "设备故障类",
      line: "申松线",
      dateFeature: "周末",
      reason: "信号系统故障",
      delay: "30分钟以上",
      handling: "处置充分",
    },
    {
      id: 3,
      title: "0723乘客滞留事件",
      level: "较大事故",
      nature: "运营类",
      line: "徐泾线",
      dateFeature: "节假日",
      reason: "客流激增",
      delay: "45分钟以上",
      handling: "处置不足",
    },
    {
      id: 4,
      title: "0805道岔故障事件",
      level: "一般A类事故",
      nature: "设备故障类",
      line: "嘉定线",
      dateFeature: "工作日",
      reason: "道岔机械故障",
      delay: "20分钟以上",
      handling: "处置充分",
    },
    {
      id: 5,
      title: "0504车辆故障救援事件",
      level: "一般A类事故",
      nature: "自然灾害类",
      line: "浦江线",
      dateFeature: "工作日",
      reason: "车辆故障",
      delay: "15分钟以上",
      handling: "处置不足",
    },
    {
      id: 6,
      title: "0612信号系统故障",
      level: "一般B类事故",
      nature: "设备故障类",
      line: "申松线",
      dateFeature: "周末",
      reason: "信号系统故障",
      delay: "30分钟以上",
      handling: "处置充分",
    },
    {
      id: 7,
      title: "0723乘客滞留事件",
      level: "较大事故",
      nature: "运营类",
      line: "徐泾线",
      dateFeature: "节假日",
      reason: "客流激增",
      delay: "45分钟以上",
      handling: "处置不足",
    },
    {
      id: 8,
      title: "0805道岔故障事件",
      level: "一般A类事故",
      nature: "设备故障类",
      line: "嘉定线",
      dateFeature: "工作日",
      reason: "道岔机械故障",
      delay: "20分钟以上",
      handling: "处置充分",
    },
  ];
  const handleStartCompare = () => {
    if (selectedEventIds.length < 2) {
      messageApi.error("请选择2个案例");
      return;
    }
    setIsCaseDetail(true);
  };
  const handleEventSelect = useCallback(
    (event: EventItem) => {
      const isSelected = selectedEventIds.includes(event.id);
      const isAlreadyAdded = addEventData.some((item) => item.id === event.id);
      if (addEventData.length >= 4 && !isSelected) {
        messageApi.error("最多只能添加4个案例");
        return;
      }
      if (isSelected) {
        setSelectedEventIds(selectedEventIds.filter((id) => id !== event.id));
        setAddEventData(addEventData.filter((item) => item.id !== event.id));
      } else {
        setSelectedEventIds([...selectedEventIds, event.id]);
        if (!isAlreadyAdded) {
          setAddEventData([...addEventData, event]);
        }
      }
    },
    [selectedEventIds, addEventData]
  );
  useEffect(() => {
    setAddEventData([]);
  }, []);

  return (
    <ConfigProvider theme={darkTheme}>
      <Content className={styles.content}>
        {contextHolder}
        {isCaseDetail && <CaseDetail />}

        {!isCaseDetail && (
          <div className={styles.contentText}>
            <div className={styles.title}>
              <img src={IMAGES.siftIcon} alt="" />
              案例对比
            </div>
            <div className={styles.contentList}>
              <div className={styles.contentTextTop}>
                <div className={styles.contentTextTopItem}>
                  {addEventData.map((event) => (
                    <div className={styles.contentTextItem} key={event.id}>
                      <div className={styles.contentTexts}>
                        <div className={styles.contentTextLeft}>
                          <img src={IMAGES.cart} alt="" />
                        </div>
                        <div className="w-[70%]">
                          <div className={styles.contentTextTitle}>
                            {event.title}
                            <img src={IMAGES.shutDown} alt="" />
                          </div>
                          <div className={styles.contentTextContent}>
                            <div className={styles.contentTextP}>
                              所属线路：
                              <span className={styles.contentTextSpan}>
                                {event.line}
                              </span>
                            </div>
                            <div className={styles.contentTextP}>
                              日期特征：
                              <span className={styles.contentTextSpan}>
                                {event.dateFeature}
                              </span>
                            </div>
                            <div className={styles.contentTextP}>
                              事件原因：
                              <button className={styles.soButton}>
                                {event.level}
                              </button>
                              <button className={styles.nature}>
                                {event.nature}
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                  {addEventData.length < 4 && (
                    <div className={styles.contentAdd}>可添加对比案例</div>
                  )}
                </div>
                <div className={styles.contrastButtonGroup}>
                  <button
                    onClick={handleStartCompare}
                    className={styles.contrastButton}
                  >
                    开始对比
                  </button>
                  <button className={styles.contrastButton}>清空对比</button>
                </div>
              </div>
              <div className={styles.contentTextBottom}>
                <div className={styles.leftPanelTitle}>
                  <div className={styles.leftPanelTitleLeft}>
                    <img src={IMAGES.contrastIcon} alt="" />
                    案例筛选
                  </div>
                  <div className="w-[80%] flex justify-around items-center">
                    <CustomSelect
                      defaultValue="all"
                      style={{ width: 180 }}
                      onOpenChange={(open) => setSelect1Open(open)}
                      selectOpen={select1Open}
                      options={[
                        { value: "all", label: "全部年份" },
                        { value: "2025", label: "2025年" },
                        { value: "2024", label: "2024年" },
                      ]}
                    />
                    <CustomSelect
                      defaultValue="all"
                      style={{ width: 180 }}
                      onOpenChange={(open) => setSelect2Open(open)}
                      selectOpen={select2Open}
                      options={[
                        { value: "all", label: "全部线路" },
                        { value: "pujiang", label: "浦江线" },
                        { value: "shensong", label: "申松线" },
                        { value: "xujing", label: "徐泾线" },
                      ]}
                    />
                    <CustomSelect
                      defaultValue="all"
                      style={{ width: 180 }}
                      onOpenChange={(open) => setSelect3Open(open)}
                      selectOpen={select3Open}
                      options={[
                        { value: "all", label: "处置不足类型" },
                        { value: "type1", label: "测试类型1" },
                        { value: "type2", label: "测试类型2" },
                      ]}
                    />
                    <CustomSelect
                      defaultValue="all"
                      style={{ width: 180 }}
                      onOpenChange={(open) => setSelect4Open(open)}
                      selectOpen={select4Open}
                      options={[
                        { value: "all", label: "全部日期特征" },
                        { value: "type1", label: "特征1" },
                        { value: "type2", label: "特征2" },
                      ]}
                    />
                    <CustomSelect
                      defaultValue="all"
                      style={{ width: 180 }}
                      onOpenChange={(open) => setSelect5Open(open)}
                      selectOpen={select5Open}
                      options={[
                        { value: "all", label: "全部事件原因" },
                        { value: "type1", label: "事件原因1" },
                        { value: "type2", label: "事件原因2" },
                      ]}
                    />
                    <CustomSelect
                      defaultValue="all"
                      style={{ width: 180 }}
                      onOpenChange={(open) => setSelect6Open(open)}
                      selectOpen={select6Open}
                      options={[
                        { value: "all", label: "全部最大晚点" },
                        { value: "type1", label: "晚点1" },
                        { value: "type2", label: "晚点2" },
                      ]}
                    />
                    <div className={styles.searchWrapper}>
                      <input
                        type="text"
                        placeholder="输入搜索关键词"
                        className={styles.searchInput}
                      />
                      <button className={styles.searchButtonInside}>
                        <SearchOutlined style={{ fontSize: "18px" }} />
                      </button>
                    </div>
                  </div>
                </div>
                <div className={styles.listContainer}>
                  {eventData.map((event) => (
                    <EventListItem
                      key={event.id}
                      event={event}
                      selected={selectedEventIds.includes(event.id)}
                      onClick={() => handleEventSelect(event)}
                    />
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}
      </Content>
    </ConfigProvider>
  );
};

export default Contrast;
