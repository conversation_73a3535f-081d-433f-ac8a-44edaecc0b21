:root {
  --primary-bg: #1f375b;
  --primary-color: rgba(14, 197, 236, 0.96);
  --secondary-color: rgba(49, 190, 255, 0.96);
  --primary-colors: rgba(239, 252, 254, 0.96);
  --accent-color: #19ebff;
  --text-shadow: 0px 0px 9px rgba(30, 198, 255, 0.8);
  --border-radius: 4px;
  --gap: 16px;
  --header-height: 104px;
}

@mixin background-image($url) {
  background-image: url($url) !important;
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}

@mixin title-style($font-size: 24px) {
  font-family: "PingFangSC", "PingFang SC";
  font-size: $font-size;
  font-style: normal;
  background-image: linear-gradient(
    0deg,
    var(--primary-color) 0%,
    var(--secondary-color) 0%,
    var(--primary-colors) 50%
  );
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.button-common {
  width: 130px !important;
  height: 52px !important;
  background-size: 100% 100% !important;
  background-position: center !important;
  background-repeat: no-repeat !important;
  transition: all 0.3s ease;
}
.button-tap {
  width: 130px;
  height: 31px;
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
  transition: all 0.3s ease;
}
.header {
  width: 100%;
  padding: 0 20px;
  height: var(--header-height);
  @include background-image("@/assets/images/traffic/herderBg.png");
  display: flex;
  color: aliceblue;
  justify-content: space-between;

  .title {
    width: 30%;
    height: 80%;
    font-weight: 500;
    @include title-style(36px);
    display: flex;
    align-items: center;
    justify-content: left;
    font-family: "AlimamaShuHeiTi";
  }

  .navGroup {
    width: 22%;
    height: 78%;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .navButton {
      font-size: 16px;
      line-height: 52px;
      @extend .button-common;
      @include background-image("@/assets/images/traffic/herButton.png");
      &:hover,
      &:active,
      &:focus {
        @include background-image("@/assets/images/traffic/herCheckBut.png");
      }
    }
  }

  .tapGroup {
    width: 20%;
    height: 78%;
    display: flex;
    justify-content: left;
    align-items: center;

    .tapButton {
      font-size: 14px;
      padding-bottom: 20px;
      @extend .button-tap;
      @include background-image("@/assets/images/traffic/herTaps.png");
      &:hover,
      &:active,
      &:focus {
        @include background-image("@/assets/images/traffic/herCheckTaps.png");
      }
      img {
        margin-top: 5%;
      }
    }
  }
  .empty {
    min-width: 15%;
    height: 78%;
    display: flex;
    justify-content: right;
    align-items: center;
  }
  .adminButton {
    margin-left: 20px;
    @extend .button-common;
    @include background-image("@/assets/images/traffic/backEnd.png");
    &:hover,
    &:active,
    &:focus {
      @extend .button-common;
      @include background-image("@/assets/images/traffic/backEnd.png");
    }
  }
}

@media (max-width: 1200px) {
  .header {
    .title {
      font-size: 28px;
    }

    .navButton,
    .tapButton,
    .adminButton {
      width: 100px;
      height: 40px;
    }
  }
}

@media (max-width: 768px) {
  .header {
    flex-direction: column;
    height: auto;
    padding: 10px;

    .title,
    .navGroup,
    .tapGroup {
      width: 100%;
      justify-content: center;
      margin-bottom: 10px;
    }
  }
}
